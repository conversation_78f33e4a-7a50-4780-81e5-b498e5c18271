/* eslint-disable no-nested-ternary */
import * as React from 'react';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';
import Page from '@/components/shared/Page';
import styled from 'styled-components';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
// import { Enroll } from '@/features/ManageFee/Enroll';
// import { MappedList } from '@/features/ManageFee/Mappedlist/MappedList';
// import { TermswiseFeePaid } from '@/features/ManageFee/TermswiseFeePaid';
// import { TermswiseFeePending } from '@/features/ManageFee/TermswiseFeePending';
// import { FeePaidClassWise } from '@/features/ManageFee/FeePaidClassWise';
import { Link, useLocation, useOutletContext, Outlet } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { flushStore } from '@/store/flush.slice';
import { OutletContextType } from '@/types/Common';
import PayFeeCollection from '@/features/ManageFee/FeeCollection/PayFeeCollection/PayFeeCollection';
import FeesDetails from '@/features/ManageFee/FeesDetails/FeesDetails';
import { useFeeRouteHistory } from '@/hooks/useFeeRouteHistory';

// Extended context type for fee management routes
export interface FeeManagementOutletContext extends OutletContextType {
  payView: 'FeeCollection' | 'PayFeeCollection' | 'PayFeeCollection2';
  handlePayView: () => void;
  handlePayView2: () => void;
  handleBackFromPayView: () => void;
  handleShowPayFee: () => void;
  navigateWithHistory: (path: string, options?: { replace?: boolean; state?: any }) => void;
  goBack: () => boolean;
  canGoBack: () => boolean;
}

const ManageFeeRoot = styled.div`
  min-height: calc(100vh - ${TOP_BAR_HEIGHT});
  background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.white : props.theme.palette.grey[900]};
  .tab {
    background-color: ${(props) => (props.theme.themeMode === 'light' ? '#fff' : props.theme.palette.grey[800])};
    /* top: 5rem; */
    /* @media screen and (max-width: 1350px) {
      width: 100%;
    } */
    /* @media screen and (max-width: 720px) {
      width: 100%;
    } */
  }
  .Mui-selected {
    color: ${(props) => props.theme.palette.primary.main};
  }
  .MuiTab-root:hover {
    color: ${(props) => props.theme.palette.primary.main};
  }
  .MuiTabScrollButton-root {
    /* background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[300] : props.theme.palette.grey[900]}; */
    width: 30px;
    height: 30px;
    margin: 5px;
    color: ${(props) => props.theme.palette.primary.main};
  }
  .MuiTabScrollButton-root:hover {
    transition: 0.1s;
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[300] : props.theme.palette.grey[900]};
  }
  /* @media screen and (max-width: 1262px) {
    .tab {
      top: 6.2rem;
    }
  } */
`;


function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`,
  };
}

export default function ManageFee() {
  const { topBarHeight } = useOutletContext<OutletContextType>();
  const location = useLocation();
  const [value, setValue] = React.useState(0);
  const [payView, setPayView] = React.useState<'FeeCollection' | 'PayFeeCollection' | 'PayFeeCollection2'>(
    'FeeCollection'
  );

  // Use fee route history management
  const { navigateWithHistory, goBack, canGoBack } = useFeeRouteHistory({
    preserveScrollPosition: true,
    enableBackNavigation: true,
  });

  const handlePayView = React.useCallback(() => {
    setPayView('PayFeeCollection');
  }, []);
  const handlePayView2 = React.useCallback(() => {
    setPayView('PayFeeCollection2');
  }, []);

  const handleBackFromPayView = () => {
    setPayView('FeeCollection');
  };

  const dispatch = useDispatch();

  React.useEffect(() => {
    dispatch(flushStore());
    console.log('Flush Store');
  }, [dispatch, value]);

  React.useEffect(() => {
    const currentPath = location.pathname;
    const routePaths = [
      '/manage-fee/overview',
      // '/manage-fee/basic-fee-list',
      // '/manage-fee/term-fee-list',
      '/manage-fee/fee-lists/basic',
      '/manage-fee/fee-settings/fee-amount',
      '/manage-fee/fee-paid-lists/total',
      '/manage-fee/fee-pending-lists/total',
      // '/manage-fee/fee-date-settings',
      // '/manage-fee/optional-fee-settings',
      // '/manage-fee/scholarship',
      '/manage-fee/total-paid-list',
      // '/manage-fee/basicfee-paid-list',
      // '/manage-fee/termfee-paid-list',
      // '/manage-fee/daily-report',
      '/manage-fee/total-pending-list',
      // '/manage-fee/total-pending-basic-list',
      // '/manage-fee/total-pending-term-list',
      '/manage-fee/collection',
      '/manage-fee/pay-fee-details',
      // '/manage-fee/fine-setting',
      '/manage-fee/bus-stop-settings',
      // '/manage-fee/individual-fee-settings',
      // '/manage-fee/rnd',
      // '/manage-fee/pay-fee',
      // '/manage-fee/fee-report',
      // '/manage-fee/optional',
      // '/manage-fee/fine-settings2',
      // '/manage-fee/fine-settings3',
      // '/manage-fee/optional-fee-report',

      // '/manage-fee/fees-details',
      // '/manage-fee/scholarship-settings',
    ];
    const index = routePaths.indexOf(currentPath);
    if (index !== -1) {
      setValue(index);
    }
  }, [location.pathname]);

  const handleShowPayFee = () => {
    setValue(14);
  };
  return (
    <Page title="Manage Fee">
      <ManageFeeRoot>
        <Tabs
          className="tab"
          variant="scrollable"
          scrollButtons
          // allowScrollButtonsMobile
          value={value}
          aria-label="basic tabs"
          sx={{
            alignItems: 'center',
            ml: '0px',
            borderBottom: 1,
            borderColor: 'divider',
            position: 'fixed',
            top: `${topBarHeight}px`,
            // left: 0,
            zIndex: 1,
            width: { xs: '100%', md: 'calc(100% - 240px)' },
            display: payView === 'FeeCollection' ? '' : 'none',
          }}
        >
          <Tab {...a11yProps(0)} label="Overview" component={Link} to="/manage-fee/overview" />
          {/* <Tab {...a11yProps(1)} label="Basic Fee" component={Link} to="/manage-fee/basic-fee-list" /> */}
          <Tab {...a11yProps(1)} label="Fee List" component={Link} to="/manage-fee/fee-lists/basic" />
          {/* <Tab {...a11yProps(2)} label="Term Fee" component={Link} to="/manage-fee/term-fee-list" /> */}
          {/* <Tab {...a11yProps(3)} label="Basic Fee Amount Settings" component={Link} to="/manage-fee/setting" /> */}
          <Tab {...a11yProps(2)} label="Fee Settings" component={Link} to="/manage-fee/fee-settings/fee-amount" />
          {/* <Tab {...a11yProps(4)} label="Term Fee Amount Settings" component={Link} to="/manage-fee/fee-date-settings" /> */}
          {/* <Tab {...a11yProps(5)} label="Optional Fee Settings" component={Link} to="/manage-fee/optional-fee" /> */}
          {/* <Tab {...a11yProps(17)} label="Individual Fee Settings" component={Link} to="/manage-fee/individual-fee" /> */}
          {/* <Tab {...a11yProps(6)} label="Scholarship Settings" component={Link} to="/manage-fee/scholarship" /> */}
          {/* <Tab {...a11yProps(15)} label="Fine Setting" component={Link} to="/manage-fee/fine-setting" /> */}
          <Tab {...a11yProps(3)} label="Fee Paid List" component={Link} to="/manage-fee/fee-paid-lists/total" />
          {/* <Tab {...a11yProps(8)} label="Total Fee Wise Paid List" component={Link} to="/manage-fee/total-paid-list" /> */}
          {/* <Tab {...a11yProps(9)} label="Basic Fee Wise Paid List" component={Link} to="/manage-fee/basicfee-list" /> */}
          {/* <Tab {...a11yProps(10)} label="Term Fee Wise Paid List" component={Link} to="/manage-fee/termfee-paid-list" /> */}
          <Tab {...a11yProps(4)} label="Fee Pending List" component={Link} to="/manage-fee/fee-pending-lists/total" />
          {/* <Tab {...a11yProps(11)} label="Total Fee Wise Pending List" component={Link} to="/manage-fee/total-pending-list" /> */}
          {/* <Tab {...a11yProps(12)} label="Basic Fee Wise Pending List" component={Link} to="/manage-fee/pending-basic" /> */}
          {/* <Tab {...a11yProps(13)} label="Term Fee Wise Pending List" component={Link} to="/manage-fee/pending-term" /> */}
          <Tab {...a11yProps(5)} label="Fee Collection" component={Link} to="/manage-fee/collection" />
          <Tab {...a11yProps(6)} label="Pay Fee" component={Link} to="/manage-fee/pay-fee-details" />
          <Tab {...a11yProps(7)} label="Bus Stop Settings" component={Link} to="/manage-fee/bus-stop-settings" />
          {/* <Tab {...a11yProps(17)} label="RND" component={Link} to="/manage-fee/rnd" /> */}
          {/* ================== */}

          {/* <Tab {...a11yProps(7)} label="Daily Collection Report" component={Link} to="/manage-fee/daily-report" /> */}
          {/* <Tab {...a11yProps(9)} label="Fee Report" component={Link} to="/manage-fee/fee-report" /> */}
          {/* ================== */}
          {/* <Tab {...a11yProps(13)} label="Pay Fee2" component={Link} to="/manage-fee/pay-fee-details" />
          <Tab {...a11yProps(14)} label="Fine Setting" component={Link} to="/manage-fee/fine-setting" />
          <Tab {...a11yProps(15)} label="Optional Fees" component={Link} to="/manage-fee/optional" /> */}
          {/* ================== */}

          {/* <Tab {...a11yProps(10)} label="Fees Details" component={Link} to="/manage-fee/fees-details" /> */}

          {/* <Tab {...a11yProps(13)} label="Scholarship Settings" component={Link} to="/manage-fee/scholarship-settings" /> */}
          {/* ================== */}
          {/* <Tab {...a11yProps(16)} label="Fine Settings2" component={Link} to="/manage-fee/fine-settings2" />
          <Tab {...a11yProps(17)} label="Fine Settings3" component={Link} to="/manage-fee/fine-settings3" />
          <Tab {...a11yProps(18)} label="Optional Fee Report" component={Link} to="/manage-fee/optional-fee-report" /> */}
          {/* ================== */}

          {/* <Tab {...a11yProps(7)} label="Fees Setting" component={Link} to="/manage-fee/setting" />
          <Tab {...a11yProps(9)} label="Optional Fees" component={Link} to="/manage-fee/optional" />

          {/* <Tab label="Enroll" {...a11yProps(1)} /> */}
          {/* <Tab label="Mapped List" {...a11yProps(3)} /> */}
          {/* <Tab label="Termswise Fees Paid List" {...a11yProps(7)} /> */}
          {/* <Tab label="Termswise Fee Pending List" {...a11yProps(9)} /> */}
          {/* <Tab label="Class Wise Paid List" {...a11yProps(10)} /> */}
        </Tabs>

        {/* Render nested routes using React Router Outlet */}
        <Box
          sx={{
            position: 'relative',
            top: payView === 'FeeCollection' ? '64px' : '0px', // Account for fixed tabs
            minHeight: 'calc(100vh - 64px)',
            display: payView === 'FeeCollection' ? 'block' : 'none',
          }}
        >
          <Outlet
            context={{
              topBarHeight,
              payView,
              handlePayView,
              handlePayView2,
              handleBackFromPayView,
              handleShowPayFee,
              navigateWithHistory,
              goBack,
              canGoBack,
            }}
          />
        </Box>

        {/* Special handling for PayFeeCollection views */}
        {payView !== 'FeeCollection' && (
          <Box sx={{ minHeight: '100vh' }}>
            {payView === 'PayFeeCollection' ? (
              <PayFeeCollection onBackClick={handleBackFromPayView} />
            ) : (
              <FeesDetails onBackClick={handleBackFromPayView} />
            )}
          </Box>
        )}
      </ManageFeeRoot>
    </Page>
  );
}
