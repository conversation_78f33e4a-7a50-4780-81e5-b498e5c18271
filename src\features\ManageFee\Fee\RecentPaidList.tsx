/* eslint-disable no-nested-ternary */
/* eslint-disable prefer-template */
import {
  Paper,
  Stack,
  Typography,
  SelectChangeEvent,
  MenuItem,
  Chip,
  Box,
  Select,
  Button,
  Avatar,
} from '@mui/material';
import CurrencyRupeeIcon from '@mui/icons-material/CurrencyRupee';
import PictureAsPdfRoundedIcon from '@mui/icons-material/PictureAsPdfRounded';
import styled, { useTheme } from 'styled-components';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import useAuth from '@/hooks/useAuth';
import PrintIcon from '@mui/icons-material/Print';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { fetchClassList } from '@/store/Dashboard/dashboard.thunks';
import { fetchFeeOverviewPaidList } from '@/store/ManageFee/manageFee.thunks';
import { getClassData, getFeeOverviewPaidListData, getFeeOverviewPaidListStatus } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import { FeeOverviewPaidListRequest, GetFeeOverviewPaidListType, GetStudentPickerDataType } from '@/types/ManageFee';
import { ClassListInfo } from '@/types/AcademicManagement';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import { PayDrawer } from '@/components/shared/Popup/PayDrawer';
import { OverViewProps } from '@/types/Common';
import { ReceiptPDF } from '@/components/shared/ReceiptPDF';
import Popup from '@/components/shared/Popup/Popup';
import dayjs from 'dayjs';
import DatePickers from '@/components/shared/Selections/DatePicker';
import StudentsPickerField from '@/components/shared/Selections/StudentsPicker';
import DTVirtuoso from '@/components/shared/RND/DataTableVirtuoso';
import { RecentPaidCardList } from './RecentPaidCardList';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

const RecentPaidListRoot = styled.div`
  display: flex;
  flex-direction: column;
  /* height: 720px; */
  /* height: calc(100vh - 20px); */
  height: calc(100vh - 155px);
  @media screen and (max-width: 576px) {
    height: calc(100vh - 100px);
  }
  .card-main-body {
    display: flex;
    flex-direction: column;
    /* max-height: calc(100% - 10px); */
    height: 100%;
    flex-grow: 1;

    .card-table-container {
      flex-grow: 1;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      /* border: 1px solid ${(props) => props.theme.palette.grey[200]}; */
      overflow: hidden;
      .MuiTableContainer-root {
        height: 100%;
      }

      .MuiTablePagination-root {
        flex-grow: 1;
        flex-shrink: 0;
      }
    }
    .MuiTableCell-head:nth-child(1) {
      padding: 4px;
      z-index: 1111;
      position: sticky;
      left: 0;
      width: 50px;
      /* padding-left: 15px; */
      background-color: ${(props) =>
        props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
    }
    .MuiTableCell-head:nth-child(2) {
      z-index: 111;
      position: sticky;
      left: 93px;
      /* width: 200px; */
      background-color: ${(props) =>
        props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
    }
    .MuiTableCell-head:nth-child(3) {
      z-index: 111;
      position: sticky;
      left: 310px;
      /* width: 60px; */
      background-color: ${(props) =>
        props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
    }
    .MuiTableCell-body:nth-child(1) {
      position: sticky;
      left: 0;
      /* width: 50px; */
      background-color: ${(props) =>
        props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
    }
    .MuiTableCell-body:nth-child(2) {
      position: sticky;
      left: 93px;
      /* width: 200px; */
      background-color: ${(props) =>
        props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
    }
    .MuiTableCell-body:nth-child(3) {
      position: sticky;
      left: 310px;
      /* width: 90px; */
      background-color: ${(props) =>
        props.theme.themeMode === 'light' ? props.theme.palette.common.white : props.theme.palette.grey[800]};
    }
    @media screen and (max-width: 992px) {
      .MuiTableCell-head:nth-child(1) {
        position: initial;
      }
      .MuiTableCell-head:nth-child(2) {
        position: initial;
      }
      .MuiTableCell-head:nth-child(3) {
        position: initial;
      }
      .MuiTableCell-body:nth-child(1) {
        position: initial;
      }
      .MuiTableCell-body:nth-child(2) {
        position: initial;
      }
      .MuiTableCell-body:nth-child(3) {
        position: initial;
      }
    }
  }
`;

export const RecentPaidList = ({ isCardView, academicId, feeTypeId }: OverViewProps) => {
  const { user } = useAuth();
  const theme = useTheme();
  const adminId: number | undefined = user?.accountId;
  const dispatch = useAppDispatch();

  const [receiptPopupOpen, setReceiptPopupOpen] = React.useState<boolean>(false);
  const [receiptOpen, setReceiptOpen] = React.useState<boolean>(false);
  const [receiptId, setReceiptId] = React.useState<number>(0);
  const [receipts, setReceipt] = React.useState<'print' | 'unpay' | 'pdf' | 'approve' | ''>('');
  const [currentPage, setCurrentPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(50);
  const [dateFilter, setDateFilter] = useState('');
  const [selectedStudentIds, setSelectedStudentIds] = useState<string>('');

  const toggleReceiptOpen = useCallback(
    (id: number, receipt: 'print' | 'unpay' | 'pdf' | 'approve' | '') => {
      setReceiptId(id);
      setReceipt(receipt);
      // if ((receipts && receipts === 'print') || (receipts && receipts === 'unpay')) {
      setReceiptOpen(true);
      // }
    },
    [setReceiptId, setReceiptOpen, setReceipt]
  );

  const popupReceiptOpen = useCallback(
    (id: number, receipt: 'print' | 'unpay' | 'pdf' | 'approve' | '') => {
      setReceiptId(id);
      setReceipt(receipt);
      setReceiptPopupOpen(true);
    },
    [setReceiptId, setReceiptPopupOpen, setReceipt]
  );

  const toggleReceiptClose = () => setReceiptOpen(false);

  const feeOverviewPaidListData = useAppSelector(getFeeOverviewPaidListData);
  const feeOverviewPaidListStatus = useAppSelector(getFeeOverviewPaidListStatus);
  // const ClassStatus = useAppSelector(getClassStatus);
  const ClassData = useAppSelector(getClassData);
  const AllClassOption = {
    classId: -1,
    className: 'All Class',
    classDescription: 'string',
    classStatus: 1,
  };

  const classDataWithAllClass = [AllClassOption, ...ClassData];
  const [classOptions, setClassOptions] = useState(classDataWithAllClass[0]);
  const { classId, className } = classOptions || {};

  const handleChange = (event: SelectChangeEvent) => {
    const selectedClass = classDataWithAllClass.find((item) => item.className === event.target.value);
    if (selectedClass) {
      setClassOptions(selectedClass);
    }
  };
  const currentRecentPaidListRequest = React.useMemo(
    () => ({
      adminId,
      academicId,
      classId,
      feeTypeId,
      studentId: selectedStudentIds,
      paidDate: dateFilter,
    }),
    [adminId, academicId, classId, feeTypeId, selectedStudentIds, dateFilter]
  );
  const loadRecentPaidList = useCallback(
    async (request: FeeOverviewPaidListRequest) => {
      try {
        const data = await dispatch(fetchFeeOverviewPaidList(request)).unwrap();
        console.log('data::::', data);
        console.log('selectedStudentIds::::', selectedStudentIds);
      } catch (error) {
        console.error('Error loading term fee list:', error);
      }
    },
    [dispatch, selectedStudentIds]
  );
  useEffect(() => {
    dispatch(fetchClassList(adminId));
    loadRecentPaidList(currentRecentPaidListRequest);
  }, [dispatch, adminId, academicId, classId, feeTypeId, loadRecentPaidList, currentRecentPaidListRequest]);

  const handlePageChange = useCallback(
    (event: any, newPage: any) => {
      setCurrentPage(newPage); // Update current page
    },
    [setCurrentPage]
  );

  const handleChangeRowsPerPage = useCallback(
    (event: any) => {
      setRowsPerPage(parseInt(event.target.value, 10)); // Update rows per page
      // Reset to first page when changing rows per page
    },
    [setRowsPerPage]
  );
  // Calculate the paginated data
  const paginatedData = feeOverviewPaidListData.slice(
    currentPage * rowsPerPage,
    currentPage * rowsPerPage + rowsPerPage
  );

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 25, 30, 50, 100, 200, 300, 500],
      pageNumber: currentPage,
      pageSize: rowsPerPage,
      totalRecords: feeOverviewPaidListData.length,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange, currentPage, rowsPerPage, feeOverviewPaidListData]
  );

  // ✅ Export to PDF (Excludes "Actions" column)
  const handleExportPDF = () => {
    const doc = new jsPDF();
    const tableColumn = [
      'Sl No',
      'Student Name',
      'Class',
      'Paid Date',
      'Amount',
      'Status',
      'Order No.',
      'Transaction Id',
    ];

    const tableRows = feeOverviewPaidListData.map((row) => [
      row.receiptId,
      row.studentName,
      row.className,
      dayjs(row.paidDate).format('DD/MM/YYYY, h:mm a'),
      row.grandTotal,
      row.status,
      row.billdeskOrderNumber || '',
      row.billdeskTransactionId || '',
    ]);

    autoTable(doc, {
      head: [tableColumn],
      body: tableRows,
      startY: 8,
      margin: { left: 8, right: 8 }, // ✅ Correct way to set horizontal start position
      styles: { fontSize: 9 },
      headStyles: { fillColor: [22, 160, 133] },
    });
    doc.save('RecentPaidList.pdf');
  };

  const pendingFeeListColumns: DataTableColumn<GetFeeOverviewPaidListType>[] = useMemo(
    () => [
      {
        name: 'receiptId',
        headerLabel: 'Sl No',
        renderCell: (row, index) => {
          return (
            <Typography minWidth={50} fontSize={13} variant="subtitle1">
              {row.receiptId}
            </Typography>
          );
        },
        sortable: true,
      },
      {
        name: 'studentName',
        headerLabel: 'Student Name',
        renderCell: (row) => {
          return (
            <Stack direction="row" minWidth={220} gap={1} alignItems="center">
              <Avatar alt="" src="" />
              <Typography variant="subtitle2">{row.studentName}</Typography>
            </Stack>
          );
        },
      },
      {
        name: 'className',
        headerLabel: 'Class',
        renderCell: (row) => {
          return (
            <Typography minWidth={90} fontSize={13} variant="subtitle1">
              {row.className}
            </Typography>
          );
        },
      },
      {
        name: 'paidDate',
        headerLabel: 'Paid Date',
        renderCell: (row) => {
          const formattedDate = dayjs(row.paidDate, 'YYYY-MM-DDTHH:mm:ss').format('DD/MM/YYYY, h:mm a');
          return (
            <Typography minWidth={150} fontSize={13} variant="subtitle1">
              {formattedDate}
            </Typography>
          );
        },
      },
      {
        name: 'Amount',
        headerLabel: 'Amount',
        renderCell: (row) => {
          return (
            <Stack direction="row" alignItems="center" spacing={0.1} color={theme.palette.success.main}>
              <CurrencyRupeeIcon sx={{ fontSize: 14, lineHeight: 1 }} /> {/* Adjusted font size */}
              <Typography
                variant="subtitle2"
                sx={{ fontSize: 13, lineHeight: 1.2 }} // Consistent font size and alignment
              >
                {row.grandTotal}
              </Typography>
            </Stack>
          );
        },
      },
      {
        name: 'Status',
        headerLabel: 'Status',
        renderCell: (row) => {
          return (
            <Chip
              size="small"
              sx={{ fontWeight: 600 }}
              label={
                row.status === 'Payment Initiated'
                  ? 'Payment Initiated'
                  : row.status === 'Paid'
                  ? 'Paid'
                  : row.status === 'Failed'
                  ? 'Failed'
                  : row.status === 'Cancelled'
                  ? 'Cancelled'
                  : row.status === 'Pending'
                  ? 'Pending'
                  : ''
              }
              variant="filled"
              // color={row.status === 'Paid' ? 'success' : 'error'}
              color={
                row.status === 'Payment Initiated'
                  ? 'warning'
                  : row.status === 'Paid'
                  ? 'success'
                  : row.status === 'Failed'
                  ? 'error'
                  : row.status === 'Cancelled'
                  ? 'error'
                  : row.status === 'Pending'
                  ? 'warning'
                  : 'secondary'
              }
            />
          );
        },
      },
      {
        name: 'billdeskOrderNumber',
        headerLabel: 'Order No.',
        // width: '150px',
        renderCell: (row) => {
          const splitOrderNumber = row.billdeskOrderNumber
            ? row.billdeskOrderNumber.slice(0, 16) + '\n' + row.billdeskOrderNumber.slice(16)
            : '';
          return (
            <Typography sx={{ userSelect: 'all' }} width={150} fontSize={13} variant="subtitle1">
              {splitOrderNumber}
            </Typography>
          );
        },
      },
      {
        name: 'billdeskTransactionId',
        headerLabel: 'Transaction Id',
        // width: '150px',
        renderCell: (row) => {
          const splitTransactionId = row.billdeskTransactionId
            ? row.billdeskTransactionId.slice(0, 16) + '\n' + row.billdeskTransactionId.slice(16)
            : '';
          return (
            <Typography sx={{ userSelect: 'all' }} width={150} fontSize={13} variant="subtitle1">
              {splitTransactionId}
            </Typography>
          );
        },
      },
      {
        name: '',
        headerLabel: 'Actions',
        renderCell: (row) => {
          return (
            <Stack direction="row" gap={2} alignItems="center" height={30}>
              <Button
                disabled={row.status === 'Cancelled'}
                size="small"
                color="success"
                startIcon={<PrintIcon />}
                sx={{ height: 0.8, width: 0.5 }}
                variant="outlined"
                onClick={() => toggleReceiptOpen(row.receiptId, 'print')}
              >
                Print
              </Button>
              <Button
                disabled={row.status === 'Cancelled'}
                size="small"
                color="error"
                sx={{ height: 0.8, width: 0.5 }}
                variant="outlined"
                onClick={() => toggleReceiptOpen(row.receiptId, 'unpay')}
              >
                Unpay
              </Button>
              <Button
                disabled={row.status === 'Paid'}
                size="small"
                color="primary"
                sx={{ height: 0.8, width: 0.5 }}
                variant="outlined"
                onClick={() => toggleReceiptOpen(row.receiptId, 'approve')}
              >
                Approve
              </Button>
              {/* <Link to="paid-receipt"> */}
              <Button
                size="small"
                color="info"
                disabled={row.status === 'Cancelled'}
                startIcon={<PictureAsPdfRoundedIcon />}
                sx={{ height: 0.8, width: 0.5 }}
                variant="outlined"
                // onClick={() => toggleReceiptOpen(row.receiptId, 'pdf')}
                onClick={() => popupReceiptOpen(row.receiptId, 'pdf')}
              >
                PDF
              </Button>
              {/* </Link> */}
            </Stack>
          );
        },
      },
    ],
    [toggleReceiptOpen, popupReceiptOpen, theme]
  );
  const getRowKey = useCallback((row: GetFeeOverviewPaidListType) => row.receiptId, []);
  return (
    <RecentPaidListRoot>
      {/* <Box className="" sx={{ minHeight: receipts !== '' ? '' : '100%' }}> */}
      {/* {receipts !== 'pdf' ? ( */}
      <Paper
        sx={{
          border: !isCardView ? `1px solid ${theme.palette.grey[300]}` : '',
          borderTop: isCardView ? `1px solid ${theme.palette.grey[300]}` : '',
          borderRadius: isCardView ? 0 : 1,
          width: '100%',
          height: '100%',
          overflow: 'auto',
          '&::-webkit-scrollbar': {
            width: 0,
          },
        }}
      >
        <div className="card-main-body">
          <Box display="flex" gap={2} p={2} alignItems="start" flexWrap="wrap" justifyContent="space-between">
            <Stack width={150}>
              <Typography variant="h6" fontSize={14}>
                Recent Paid List
              </Typography>
            </Stack>
            <Stack direction="row" alignItems="start" flexWrap="wrap" gap={2}>
              <Select
                sx={{
                  backgroundColor: theme.palette.common.white,
                  color: theme.palette.primary.main,
                  // height: 30,
                  width: { xs: '100%', sm: 165, xl: 140 },
                }}
                value={className}
                onChange={handleChange}
                displayEmpty
                labelId="demo-dialog-select-label"
                id="demo-dialog-select"
                inputProps={{ 'aria-label': 'Without label' }}
                MenuProps={{
                  PaperProps: {
                    style: {
                      maxHeight: '250px', // Adjust the value to your desired height
                    },
                  },
                }}
              >
                {/* <MenuItem value={className} className="d-none">
              {className}
            </MenuItem> */}
                {classDataWithAllClass?.map((item: ClassListInfo) => (
                  <MenuItem sx={{ fontSize: '13px' }} key={item.classId} value={item.className}>
                    {item.className}
                  </MenuItem>
                ))}
              </Select>
              <DatePickers
                // disabled={studentFilterData.length === 0}
                width={{ xs: '100%', sm: '170px', xl: '160px' }}
                name="fromDateFilter"
                value={dayjs(dateFilter, 'DD/MM/YYYY')}
                onChange={(e) => {
                  const formattedDate = e ? e.format('DD/MM/YYYY') : '';
                  setDateFilter(formattedDate);
                  console.log('date::::', formattedDate);
                  loadRecentPaidList({ ...currentRecentPaidListRequest, paidDate: formattedDate });
                }}
              />
              <StudentsPickerField
                loadRecentPaidList={loadRecentPaidList}
                currentRecentPaidListRequest={currentRecentPaidListRequest}
                setSelectedStudentIds={setSelectedStudentIds}
                classId={classId}
                academicId={academicId}
                multiple
              />
              {/* ✅ Export to PDF Button */}
              <Button
                disabled={feeOverviewPaidListData.length === 0}
                variant="contained"
                color="info"
                startIcon={<PictureAsPdfRoundedIcon />}
                onClick={handleExportPDF}
              >
                Export to PDF
              </Button>
            </Stack>
          </Box>

          {isCardView ? (
            <Paper sx={{ height: 510, overflow: 'auto' }}>
              <RecentPaidCardList
                data={paginatedData}
                theme={theme}
                onPrint={(row) => toggleReceiptOpen(row.receiptId, 'print')}
                onUnpay={(row) => toggleReceiptOpen(row.receiptId, 'unpay')}
                onApprove={(row) => toggleReceiptOpen(row.receiptId, 'approve')}
                onPdf={(row) => popupReceiptOpen(row.receiptId, 'pdf')}
                classId={classId}
              />
            </Paper>
          ) : (
            <Paper className="card-table-container">
              <DTVirtuoso
                tableStyles={{ minWidth: { xs: '1350px' } }}
                showHorizontalScroll
                columns={pendingFeeListColumns}
                data={paginatedData}
                getRowKey={getRowKey}
                fetchStatus={feeOverviewPaidListStatus}
                PaginationProps={pageProps}
                allowPagination
              />
            </Paper>
          )}

          {/* <Paper className="card-table-container">
            <DTVirtuoso
              // tableStyles={{ minWidth: { xs: '1300px' } }}
              tableStyles={{ minWidth: { xs: '1350px' } }}
              showHorizontalScroll
              columns={pendingFeeListColumns}
              data={paginatedData}
              getRowKey={getRowKey}
              fetchStatus={feeOverviewPaidListStatus}
              PaginationProps={pageProps}
              allowPagination
            />
          </Paper> */}
        </div>
      </Paper>
      {/* // ) : (
      //   <ReceiptPDF onClose={() => setReceipt('')} receiptIdNo={receiptId} />
      // )} */}
      {/* </Box> */}

      {receipts !== 'pdf' && (
        <TemporaryDrawer
          closeIconDisable
          onClose={toggleReceiptClose}
          state={receiptOpen}
          DrawerContent={
            <PayDrawer
              load={() => loadRecentPaidList(currentRecentPaidListRequest)}
              receiptDrawer={receipts}
              onClose={toggleReceiptClose}
              receiptIdNo={receiptId}
            />
          }
        />
      )}

      <Popup
        size="xl"
        state={receiptPopupOpen}
        popupContent={<ReceiptPDF receiptIdNo={receiptId} onClose={() => setReceiptPopupOpen(false)} />}
      />
    </RecentPaidListRoot>
  );
};
