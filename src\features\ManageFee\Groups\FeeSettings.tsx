/* eslint-disable no-nested-ternary */
import React, { useState, useEffect, Component } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import Page from '@/components/shared/Page';
import { Box, ToggleButtonGroup, ToggleButton, Typography, useTheme } from '@mui/material';
import styled from 'styled-components';
import BasicFeeSetting from '@/features/ManageFee/BasicFeeSetting/BasicFeeSetting';
import FeeDateSettings from '@/features/ManageFee/FeeDateSetting/FeeDateSettings';
import Scholarship from '@/features/ManageFee/Scholarship/Scholarship';
import OptionalFeeSetting from '@/features/ManageFee/OptionalFee/OptionalFeeSetting';
import Fine from '@/features/ManageFee/Fine/Fine';
import IndividualFeeSetting from '@/features/ManageFee/IndividualFeeSetting';
import OptionalFeeSetting2 from '@/features/ManageFee/OptionalFeeSetting2';

const FeeSettingsGroupRoot = styled.div`
  padding: 0.5rem 1rem 1rem 1rem;
  @media screen and (max-width: 576px) {
    padding: 0.5rem;
  }

  .toggle-container {
    padding-bottom: 0.5rem;
    padding-left: 0.5rem;
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[100] : props.theme.palette.grey[900]};
    border-radius: 8px;
  }

  .content-container {
    /* Remove Page wrapper padding since components have their own */
    margin: -1rem;
    @media screen and (max-width: 576px) {
      margin: -0.5rem;
    }
  }
`;

// type FeeType = 'amount' | 'date' | 'scholarship' | 'fine' | 'optional' | 'individual' | 'busstop';
const options = [
  { path: '/manage-fee/fee-settings/fee-amount', Component: <BasicFeeSetting /> },
  { path: '/manage-fee/fee-settings/term-fee', Component: <FeeDateSettings /> },
  { path: '/manage-fee/fee-settings/scholarship', Component: <Scholarship /> },
  { path: '/manage-fee/fee-settings/fine', Component: <Fine /> },
  { path: '/manage-fee/fee-settings/optional-fee', Component: <OptionalFeeSetting /> },
  { path: '/manage-fee/fee-settings/individual-fee', Component: <IndividualFeeSetting /> },
];
function FeeSettingsGroup() {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const [feeType, setFeeType] = useState(0);

  // Sync URL with fee type selection

  const handleFeeTypeChange = React.useCallback(
    (event: React.MouseEvent<HTMLElement> | null, newFeeType: number | null) => {
      if (newFeeType !== null) {
        setFeeType(newFeeType);
        // Update URL based on selection
        if (newFeeType === 0) {
          navigate('/manage-fee/fee-settings/fee-amount', { replace: true });
        } else if (newFeeType === 1) {
          navigate('/manage-fee/term-fee', { replace: true });
        } else if (newFeeType === 2) {
          navigate('/manage-fee/scholarship', { replace: true });
        } else if (newFeeType === 3) {
          navigate('/manage-fee/fine-setting', { replace: true });
        } else if (newFeeType === 4) {
          navigate('/manage-fee/optional-fee', { replace: true });
        } else if (newFeeType === 5) {
          navigate('/manage-fee/individual-fee-settings', { replace: true });
        }
      }
    },
    [navigate]
  );

  useEffect(() => {
    const currentPath = location.pathname;
    const index = options.findIndex((option) => option.path === currentPath);
    if (index !== -1) {
      setFeeType(index);
    }
    handleFeeTypeChange(null, index);
  }, [location.pathname, handleFeeTypeChange]);

  return (
    <Page title="Fee List Groups">
      <FeeSettingsGroupRoot>
        <div className="toggle-container">
          <Box display="flex" flexDirection="row" alignItems="center" justifyContent="center" gap={2}>
            <Typography
              variant="h6"
              fontSize={18}
              fontWeight={600}
              color={theme.palette.mode === 'light' ? 'inherit' : 'text.primary'}
            >
              Fee Mapping
            </Typography>
            <ToggleButtonGroup
              value={feeType}
              exclusive
              onChange={handleFeeTypeChange}
              aria-label="Fee Type Selection"
              size="small"
              sx={{
                '& .MuiToggleButton-root': {
                  px: 3,
                  py: 1,
                  fontWeight: 600,
                  borderRadius: '20px',
                  border: `2px solid ${theme.palette.primary.main}`,
                  color: theme.palette.primary.main,
                  '&.Mui-selected': {
                    backgroundColor: theme.palette.primary.main,
                    color: theme.palette.primary.contrastText,
                    '&:hover': {
                      backgroundColor: theme.palette.primary.dark,
                    },
                  },
                  '&:hover': {
                    backgroundColor: theme.palette.primary.light,
                    color: theme.palette.primary.contrastText,
                  },
                },
              }}
            >
              <ToggleButton value={0}>Fee Amount</ToggleButton>
              <ToggleButton value={1}>Term Fee</ToggleButton>
              <ToggleButton value={2}>Scholarship</ToggleButton>
              <ToggleButton value={3}>Fine</ToggleButton>
              <ToggleButton value={4}>Optional Fee</ToggleButton>
              <ToggleButton value={5}>Individual Fee</ToggleButton>
            </ToggleButtonGroup>
          </Box>
        </div>

        <div className="content-container">{options[feeType].Component}</div>
      </FeeSettingsGroupRoot>
    </Page>
  );
}

export default FeeSettingsGroup;
