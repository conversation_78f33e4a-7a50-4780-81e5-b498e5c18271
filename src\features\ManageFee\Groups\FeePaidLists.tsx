/* eslint-disable no-nested-ternary */
import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, Outlet } from 'react-router-dom';
import Page from '@/components/shared/Page';
import { Box, ToggleButtonGroup, ToggleButton, Typography, useTheme } from '@mui/material';
import styled from 'styled-components';

const FeePaidListsRoot = styled.div`
  padding: 0.5rem 1rem 1rem 1rem;
  @media screen and (max-width: 576px) {
    padding: 0.5rem;
  }

  .toggle-container {
    padding-bottom: 0.5rem;
    padding-left: 0.5rem;
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[100] : props.theme.palette.grey[900]};
    border-radius: 8px;
  }

  .content-container {
    /* Remove Page wrapper padding since components have their own */
    margin: -1rem;
    @media screen and (max-width: 576px) {
      margin: -0.5rem;
    }
  }
`;

const options = [
  { path: '/manage-fee/fee-paid-lists/total', label: 'Total' },
  { path: '/manage-fee/fee-paid-lists/fee', label: 'Fee Wise' },
  { path: '/manage-fee/fee-paid-lists/term', label: 'Term Wise' },
];

function FeePaidLists() {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const [feeType, setFeeType] = useState(0);

  // Sync URL with fee type selection
  useEffect(() => {
    const currentPath = location.pathname;
    const index = options.findIndex((option) => option.path === currentPath);
    if (index !== -1) {
      setFeeType(index);
    } else {
      // Default to first option if no match found
      setFeeType(0);
      navigate(options[0].path, { replace: true });
    }
  }, [location.pathname, navigate]);

  const handleFeeTypeChange = React.useCallback(
    (_event: React.MouseEvent<HTMLElement>, newFeeType: number | null) => {
      if (newFeeType !== null && newFeeType < options.length) {
        setFeeType(newFeeType);
        // Navigate to the correct nested route
        navigate(options[newFeeType].path, { replace: true });
      }
    },
    [navigate]
  );
  return (
    <Page title="Fee Paid Lists">
      <FeePaidListsRoot>
        <div className="toggle-container">
          <Box display="flex" flexDirection="row" alignItems="center" justifyContent="center" gap={2}>
            <Typography
              variant="h6"
              fontSize={18}
              fontWeight={600}
              color={theme.palette.mode === 'light' ? 'inherit' : 'text.primary'}
            >
              Fee Paid Lists
            </Typography>
            <ToggleButtonGroup
              value={feeType}
              exclusive
              onChange={handleFeeTypeChange}
              aria-label="Fee Type Selection"
              size="small"
              sx={{
                '& .MuiToggleButton-root': {
                  px: 3,
                  py: 1,
                  fontWeight: 600,
                  borderRadius: '20px',
                  border: `2px solid ${theme.palette.primary.main}`,
                  color: theme.palette.primary.main,
                  '&.Mui-selected': {
                    backgroundColor: theme.palette.primary.main,
                    color: theme.palette.primary.contrastText,
                    '&:hover': {
                      backgroundColor: theme.palette.primary.dark,
                    },
                  },
                  '&:hover': {
                    backgroundColor: theme.palette.primary.light,
                    color: theme.palette.primary.contrastText,
                  },
                },
              }}
            >
              {options.map((option, index) => (
                <ToggleButton key={option.path} value={index}>
                  {option.label}
                </ToggleButton>
              ))}
            </ToggleButtonGroup>
          </Box>
        </div>

        <div className="content-container">
          <Outlet />
        </div>
      </FeePaidListsRoot>
    </Page>
  );
}

export default FeePaidLists;
