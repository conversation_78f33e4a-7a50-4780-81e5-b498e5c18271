/* eslint-disable no-nested-ternary */
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  Typography,
  TableHead,
  TableRow,
  Button,
  Stack,
  useTheme,
  IconButton,
  TextField,
} from '@mui/material';
import React, { useCallback, useRef, useState } from 'react';
import PrintIcon from '@mui/icons-material/Print';
import CloseIcon from '@mui/icons-material/Close';
import errorIcon from '@/assets/ManageFee/Error.json';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import deleteSuccess from '@/assets/ManageFee/deleteSuccess.json';
import approveReciept from '@/assets/ManageFee/approveReciept.json';
import successIcon from '@/assets/ManageFee/Success.json';
import { useReactToPrint } from 'react-to-print';
import { ReceiptApprove, ReceiptCancel, fetchReceiptForPrint } from '@/store/ManageFee/manageFee.thunks';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import LoadingButton from '@mui/lab/LoadingButton';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getReceiptForPrintListData, getReceiptForPrintListStatus } from '@/config/storeSelectors';
import dayjs from 'dayjs';
import useAuth from '@/hooks/useAuth';
import { GetReceiptDetailsType, GetReceiptPaymentModeType, GetReceiptTermfeepaidType } from '@/types/ManageFee';
import passdailLogo from '@/assets/SchoolLogos/logo-small.svg';
import holyLogo from '@/assets/SchoolLogos/HolyLogo.jpeg';
import carmelLogo from '@/assets/SchoolLogos/CarmelLogo.png';
import thereseLogo from '@/assets/SchoolLogos/StthereseLogo.png';
import thomasLogo from '@/assets/SchoolLogos/StThomasLogo.png';
import MIMLogo from '@/assets/SchoolLogos/MIMLogo.png';
import nirmalaLogo from '@/assets/SchoolLogos/NirmalaLogo.png';
import useSettings from '@/hooks/useSettings';
import { DeleteMessage } from './DeleteMessage';
import { ErrorMessage } from './ErrorMessage';
import { Skeleton } from '@mui/material';
import { SuccessMessage } from './SuccessMessage';
import { useSchool } from '@/contexts/SchoolContext';

// Define CSS styles for print in your component file or in an external CSS file
const printStyles = `
@media print {
.Main-div{
margin-top:0px;
}
.amount_words{
max-width:95%;
}
  .print-preview-container {
    width: 95%;
height:96.5%;
position:absolute;
top:0px;
bottom:0px;
margin: 20px;
padding:20px;
border: 1px solid;
  }
  .scrollbar::-webkit-scrollbar{
    width:0px
  }
}
`;
// Inject the styles into the document head
const styleSheet = document.createElement('style');
styleSheet.type = 'text/css';
styleSheet.innerText = printStyles;
document.head.appendChild(styleSheet);

type ReaceiptPrps = {
  receiptIdNo: number;
  onClose: () => void;
  receiptDrawer?: string;
  load?: any;
};
export const PayDrawer = ({ receiptIdNo, onClose, receiptDrawer, load }: ReaceiptPrps) => {
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const { user } = useAuth();
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const adminId: number | undefined = user?.accountId;
  const { selectedSchool } = useSchool();

  const receiptForPrintListData = useAppSelector(getReceiptForPrintListData);
  const receiptForPrintListStatus = useAppSelector(getReceiptForPrintListStatus);
  const [paymodeData, setPaymodeData] = React.useState<GetReceiptPaymentModeType[]>([]);
  const [termfeepaid, setTermfeepaid] = React.useState<GetReceiptTermfeepaidType[]>([]);
  const [receiptDetails, setReceiptDetails] = React.useState<GetReceiptDetailsType | undefined>(undefined);
  const [reason, setReason] = useState('');
  const [transactionId, setTransactionId] = useState('');
  const {
    schoolName,
    schoolAddress,
    schoolEmail,
    schoolPhone,
    studentName,
    createdDate,
    grandTotal,
    receiptId,
    receiptType,
    className,
    contactNo,
    admissionNo,
    grandTotalWords,
    receiptNo,
  } = receiptDetails || {};

  const componentRef = useRef<HTMLInputElement>(null);
  const handlePrint = useReactToPrint({
    content: () => componentRef.current,
  });

  const currentReceiptListRequest = React.useMemo(
    () => ({
      adminId,
      receiptId: receiptIdNo,
    }),
    [adminId, receiptIdNo]
  );
  const loadReceiptDataList = useCallback(
    async (request: { adminId: number | undefined; receiptId: number | undefined }) => {
      try {
        const data = await dispatch(fetchReceiptForPrint(request)).unwrap();
        console.log('data::::', data);
        setReceiptDetails(data.receiptDetail);
        setPaymodeData(data.paymentmode);
        setTermfeepaid(data.termfeepaid);
      } catch (error) {
        console.error('Error loading term fee list:', error);
      }
    },
    [dispatch]
  );

  React.useEffect(() => {
    loadReceiptDataList(currentReceiptListRequest);
  }, [loadReceiptDataList, currentReceiptListRequest]);

  const formattedDate = dayjs(createdDate, 'YYYY/MM/DD').format('DD/MM/YYYY');
  const totalAmountPaid = termfeepaid?.reduce((total, item) => total + item.totalAmount, 0);

  const handleReasonChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setReason(e.target.value);
  };

  const handleTransactionIdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTransactionId(e.target.value);
  };

  const handleReceiptCancel = useCallback(async () => {
    try {
      const sendConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={
            <div style={{ color: theme.palette.error.main }}>
              Are you sure want to delete the Receipt?
              <br />
            </div>
          }
        />
      );
      if (await confirm(sendConfirmMessage, 'Delete Receipt?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = { adminId, receiptNo: receiptId, reason, deletedId: 0, dbResult: 'string' };
        const deleteResponse = await dispatch(ReceiptCancel(sendReq)).unwrap();
        console.log('deleteResponse', deleteResponse);
        if (deleteResponse.deletedId) {
          const deleteDoneMessage = (
            <DeleteMessage loop={false} jsonIcon={deleteSuccess} message="Receipt deleted successfully." />
          );
          await confirm(deleteDoneMessage, 'Deleted', { okLabel: 'Ok', showOnlyOk: true });
          onClose();
          load();
          // setMessageTempListDatas((prevMessageTempListDatas) =>
          //   prevMessageTempListDatas.filter((item) => item.messageId !== receiptobj.messageId)
          // );
        } else {
          const errorMessage = (
            <ErrorMessage loop={false} jsonIcon={errorIcon} message="Receipt deleting failed. Please try again." />
          );
          await confirm(errorMessage, 'Failed', { okLabel: 'Ok', showOnlyOk: true });
        }
        //  else {
        //   const deleteErrorMessage = <DeleteMessage message="Template not deleted." />;
        //   await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        // }
      }
    } catch (error) {
      console.error('Error during receipt cancellation:', error);

      const errorMessage = (
        <ErrorMessage
          loop={false}
          jsonIcon={errorIcon}
          message="An unexpected error occurred while deleting the receipt. Please try again."
        />
      );
      await confirm(errorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
    }
  }, [confirm, dispatch, adminId, reason, receiptId, load, onClose, theme]);

  const handleReceiptApprove = useCallback(async () => {
    try {
      const sendConfirmMessage = (
        <SuccessMessage
          jsonIcon={approveReciept}
          message={
            <div>
              Are you sure want to approve the Receipt?
              <br />
            </div>
          }
        />
      );
      if (await confirm(sendConfirmMessage, 'Approve Receipt', { okLabel: 'Ok', cancelLabel: 'Cancel' })) {
        const sendReq = { adminId, receiptNo: receiptId, reason, transactionId, aprovedId: 0, dbResult: 'string' };
        const deleteResponse = await dispatch(ReceiptApprove(sendReq)).unwrap();
        console.log('deleteResponse', deleteResponse);
        if (deleteResponse.aprovedId) {
          const deleteDoneMessage = (
            <DeleteMessage loop={false} jsonIcon={successIcon} message="Receipt approved successfully." />
          );
          await confirm(deleteDoneMessage, 'Approved', { okLabel: 'Ok', showOnlyOk: true });
          onClose();
          load();
          // setMessageTempListDatas((prevMessageTempListDatas) =>
          //   prevMessageTempListDatas.filter((item) => item.messageId !== receiptobj.messageId)
          // );
        } else {
          const errorMessage = (
            <ErrorMessage loop={false} jsonIcon={errorIcon} message="Receipt approving failed. Pleasetry again." />
          );
          await confirm(errorMessage, 'Failed', { okLabel: 'Ok', showOnlyOk: true });
        }
        //  else {
        //   const deleteErrorMessage = <DeleteMessage message="Template not deleted." />;
        //   await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        // }
      }
    } catch (error) {
      console.error('Error during receipt approving:', error);

      const errorMessage = (
        <ErrorMessage
          loop={false}
          jsonIcon={errorIcon}
          message="An unexpected error occurred while approving the receipt. Please try again."
        />
      );
      await confirm(errorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
    }
  }, [confirm, dispatch, adminId, reason, receiptId, load, onClose, transactionId]);
  return (
    <Box>
      <Box className="print-preview-container" ref={componentRef}>
        {receiptForPrintListStatus === 'loading' ? (
          <Box flexDirection="column" display="flex" gap={1} alignItems="center" mb={receiptType ? 2 : 2}>
            <Skeleton variant="circular" width={70} height={70} />
            <Skeleton variant="rounded" width={180} height={15} />
            <Skeleton variant="rounded" width={230} height={15} />
            <Skeleton variant="rounded" width={300} height={15} />
          </Box>
        ) : (
          <Box flexDirection="column" display="flex" alignItems="center" mb={receiptType ? 2 : 0}>
            <img src={selectedSchool?.schoolLogo} alt={selectedSchool?.schoolLogo} width={70} />
            {/* <img src={passdailLogo} width={70} alt="passdailLogo" /> */}
            {/* {receiptType === 'ReceiptDefault' && <img src={holyLogo} width={70} alt="holyLogo" />} */}
            {/* <img src={carmelLogo} width={70} alt="carmelLogo" /> */}
            {/* <img src={thereseLogo} width={70} alt="thereseLogo" /> */}
            {/* <img src={thomasLogo} width={70} alt="thomasLogo" /> */}
            {/* <img src={nirmalaLogo} width={70} alt="nirmalaLogo" /> */}
            {/* <img src={MIMLogo} width={70} alt="MIMLogo" /> */}

            {/* {receiptType === 'ReceiptDefault' && (
              <>
                <Typography variant="subtitle2" fontSize={16} color="primary">
                  {schoolName}
                </Typography>
                <Typography variant="body1" fontSize={13} color="secondary">
                  {schoolAddress}
                </Typography>
                <Typography variant="body1" fontSize={13} color="secondary">
                  {schoolEmail}, {schoolPhone}
                </Typography>
              </>
            )} */}
            {/* ========== MIM HIGH SCHOOL ========== */}
            {/* <>
              <Typography variant="subtitle2" fontSize={16} color="primary">
                Mueenul Islam Manoor High School
              </Typography>
              <Typography variant="body1" fontSize={13} color="secondary">
                KANDANAKAM,KALADI PO MALAPPURAM 679582
              </Typography>
              <Typography variant="body1" fontSize={13} color="secondary">
                04942103095, 9645942121
              </Typography>
            </> */}
            {/* ========== MIM HIGH SCHOOL ========== */}
            <>
              <Typography variant="subtitle2" fontSize={16} color="primary">
                Assisi English Medium Higher Secondary School
              </Typography>
              <Typography variant="body1" fontSize={13} color="secondary">
                Pudussery Kanjikode, Palakkad 678621
              </Typography>
              <Typography variant="body1" fontSize={13} color="secondary">
                0491256679, 9496069179
              </Typography>
            </>
          </Box>
        )}
        {/* ========== Holy Angels Gardens ========== */}
        {receiptType === 'ReceiptHolyNursery' && (
          <Box
            className="Main-div"
            flexDirection="row"
            display="flex"
            mt={5}
            gap={4}
            justifyContent="center"
            alignItems="center"
            p={2}
            mb={2}
            bgcolor="#e2b4bb"
          >
            <img src={holyLogo} width={100} alt="holyLogo" />
            <Stack textAlign="center" flex={1}>
              <Typography variant="h1" fontWeight="bold" fontSize={30} color="primary">
                <b>ANGELS&apos; GARDENS</b>
              </Typography>
              <Typography variant="body1" fontSize={13}>
                The Kindergarten Experience
              </Typography>
              <Typography variant="body1" fontSize={15}>
                Gandhinagar, Dombivli (E)
              </Typography>
            </Stack>
            <div style={{ visibility: 'hidden', width: 100 }} />
          </Box>
        )}
        {/* ========== Holy Angels Paradise Kindergarten ========== */}
        {/* ITH */}
        {/* {receiptType === 'ReceiptHolyKg' && (
          <Box
            className="Main-div"
            flexDirection="row"
            display="flex"
            mt={5}
            gap={4}
            justifyContent="center"
            alignItems="center"
            p={2}
            mb={2}
            bgcolor="#e2b4bb"
          >
            <img src={holyLogo} width={100} alt="holyLogo" />
            <Stack textAlign="center" flex={1}>
              <Typography variant="h1" fontWeight="bold" fontSize={30} color="primary">
                <b>ANGELS&apos; PARADISE</b>
              </Typography>
              <Typography variant="body1" fontSize={13}>
                The Kindergarten Experience
              </Typography>
              <Typography variant="body1" fontSize={15}>
                Gandhinagar, Dombivli (E)
              </Typography>
            </Stack>
            <div style={{ visibility: 'hidden', width: 100 }} />
          </Box>
        )} */}
        {/* ========== Holy Angels Paradise College ========== */}
        {receiptType === 'ReceiptHolyCollege' && (
          <Box
            className="Main-div"
            flexDirection="column"
            mt={5}
            bgcolor="yellow"
            border={1}
            p={1}
            display="flex"
            alignItems="center"
            mb={2}
          >
            <img src={holyLogo} width={70} alt="holyLogo" />
            <Stack color={theme.palette.common.black} direction="row" justifyContent="space-between" gap={5}>
              <Typography variant="subtitle2" fontSize={14}>
                Affiliated to CBSE
              </Typography>
              <Typography variant="subtitle2" fontSize={13}>
                TRINITY EDUCATIONAL TRUST&apos;S
              </Typography>
              <Typography variant="subtitle2" fontSize={14}>
                ISO 9001: 2008 Certified
              </Typography>
            </Stack>
            <Typography variant="h1" fontWeight="bold" fontSize={30} color="primary">
              <b>HOLY ANGELS&apos; SCHOOL & Jr. COLLEGE</b>
            </Typography>
            <Typography color={theme.palette.common.black} variant="body1" fontSize={13}>
              (A Private Unaided Minority Institution)
            </Typography>
            <Typography color={theme.palette.common.black} variant="body1" fontSize={9}>
              Behind P & T Colony, Nandivli - Gandhinagar, Dombivli (E) Dist. Thane, MAH
              <EMAIL> Tel: (0251) 2821975, 2821234
            </Typography>
          </Box>
        )}
        {/* ============================ */}
        <Box border={1} p={1} display="flex" justifyContent="space-between" mb={1}>
          <Stack>
            <Typography variant="subtitle1" fontSize={13}>
              Name : <span className="fw-bold">{studentName}</span>
            </Typography>
            <Typography variant="subtitle1" fontSize={13}>
              Admission No : <span className="fw-bold">{admissionNo}</span>
            </Typography>
            <Typography variant="subtitle1" fontSize={13}>
              Class Name : <span className="fw-bold">{className}</span>
            </Typography>
          </Stack>
          <Stack>
            <Typography variant="subtitle1" fontSize={13}>
              Date : <span className="fw-bold">{formattedDate}</span>
            </Typography>
            <Typography variant="subtitle1" fontSize={13}>
              Receipt No : <span className="fw-bold">{receiptNo}</span>
            </Typography>
            <Typography variant="subtitle1" fontSize={13}>
              Contact No : <span className="fw-bold">{contactNo}</span>
            </Typography>
          </Stack>
        </Box>
        {/* <Divider sx={{ my: 1 }} /> */}
        <Box
          className="scrollbar"
          sx={{
            height: 'calc(100vh - 320px)',
            overflow: 'auto',
            mb: 2,
            // '&::-webkit-scrollbar': {
            //   width: '0px',
            // },
          }}
        >
          {/* <TableContainer sx={{ height: 'calc(100vh - 470px)' }}> */}
          <TableContainer>
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ '&:first-of-type': { paddingLeft: 0 } }}>Fee Title</TableCell>
                  <TableCell>Term Title</TableCell>
                  <TableCell align="right">Amount (INR)</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {termfeepaid?.map((item) => (
                  <TableRow key={item.receiptDetailsId}>
                    <TableCell sx={{ '&:first-of-type': { paddingLeft: 0 } }} color="secondary">
                      {item.feeTitle}
                    </TableCell>
                    <TableCell color="secondary"> {item.termTitle}</TableCell>
                    <TableCell align="right"> {item.totalAmount}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          <Box sx={{ borderTop: '2px dashed gray' }} py={3} display="flex" justifyContent="space-between">
            <Typography variant="body2" fontWeight={700}>
              Total Amount Paid
            </Typography>
            <Typography variant="body2" fontWeight={700}>
              {/* {grandTotal} Display total amount */}
              {totalAmountPaid.toLocaleString()}
            </Typography>
          </Box>
          <TableContainer sx={{ mb: 2 }}>
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ '&:first-of-type': { paddingLeft: 0 } }}>Payment Mode</TableCell>
                  <TableCell>Reference No</TableCell>
                  <TableCell align="right">Amount</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paymodeData?.map((item) => (
                  <TableRow key={item.paymentModeId}>
                    <TableCell sx={{ '&:first-of-type': { paddingLeft: 0 } }} color="secondary">
                      {item.paymentType}
                    </TableCell>
                    <TableCell> {item.chequeOrDDNo}</TableCell>
                    <TableCell align="right"> {item.amount}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          <Stack className="amount_words" maxWidth={400}>
            <Typography variant="body2" pb={1} color="secondary">
              Total amount in words :&nbsp;
              <span
                className="fw-bold"
                style={{ color: isLight ? theme.palette.common.black : theme.palette.common.white }}
              >
                {grandTotalWords}
              </span>
            </Typography>
          </Stack>
        </Box>
      </Box>
      <Box sx={{ justifyContent: 'center', position: 'relative' }}>
        {receiptDrawer === 'unpay' && (
          <Stack mb={2}>
            <Typography variant="subtitle1" fontSize={13}>
              Reason for unpay
            </Typography>
            <TextField
              fullWidth
              multiline
              placeholder="Type here..."
              value={reason}
              onChange={handleReasonChange}
              InputProps={{
                inputProps: {
                  style: { resize: 'vertical', width: '100%', minHeight: '20px', maxHeight: '100px' },
                },
              }}
              size="small"
            />
          </Stack>
        )}

        {receiptDrawer === 'approve' && (
          <Stack mb={2}>
            <Typography variant="subtitle1" fontSize={13}>
              Comments
            </Typography>
            <TextField
              fullWidth
              multiline
              placeholder="Type here..."
              value={reason}
              onChange={handleReasonChange}
              InputProps={{
                inputProps: {
                  style: { resize: 'vertical', width: '100%', minHeight: '20px', maxHeight: '100px' },
                },
              }}
              size="small"
            />
            <Typography variant="subtitle1" fontSize={13}>
              Transaction Id
            </Typography>
            <TextField
              fullWidth
              placeholder="Enter Id"
              value={transactionId}
              onChange={handleTransactionIdChange}
              size="small"
            />
          </Stack>
        )}

        <Stack pb={2.5} spacing={2} direction="row" justifyContent="center" sx={{}}>
          <Button fullWidth variant="contained" color="secondary" size="medium" onClick={onClose}>
            Cancel
          </Button>
          {receiptDrawer === 'unpay' ? (
            <LoadingButton
              loadingPosition="start"
              disabled={!reason}
              // startIcon={<PrintIcon />}
              // loading={isSubmitting}
              variant="contained"
              fullWidth
              color="error"
              // sx={{ backgroundColor: '#f1f2f3', color: 'black', fontWeight: 600 }}
              size="medium"
              onClick={handleReceiptCancel}
              // onClick={!printButtonShow ? handlePay : handlePrint}
            >
              Unpay
            </LoadingButton>
          ) : receiptDrawer === 'approve' ? (
            <LoadingButton
              loadingPosition="start"
              // disabled={!reason}
              variant="contained"
              fullWidth
              color="success"
              size="medium"
              onClick={handleReceiptApprove}
            >
              Approve
            </LoadingButton>
          ) : (
            <LoadingButton
              loadingPosition="start"
              startIcon={<PrintIcon />}
              // loading={isSubmitting}
              variant="contained"
              fullWidth
              color="success"
              // sx={{ backgroundColor: '#f1f2f3', color: 'black', fontWeight: 600 }}
              size="medium"
              onClick={handlePrint}
              // onClick={!printButtonShow ? handlePay : handlePrint}
            >
              Print
            </LoadingButton>
          )}
        </Stack>
      </Box>
      <Stack sx={{ position: 'absolute', top: 10, right: 10 }}>
        <IconButton
          aria-label="close"
          onClick={() => {
            onClose();
          }}
          sx={{
            p: 1,
            color: theme.palette.grey[500],
            '&:hover': {},
          }}
        >
          <CloseIcon />
        </IconButton>
      </Stack>
    </Box>
  );
};
