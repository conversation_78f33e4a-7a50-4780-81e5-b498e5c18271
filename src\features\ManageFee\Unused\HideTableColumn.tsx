import React, { useState } from 'react';
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Button } from '@mui/material';
import styled from 'styled-components';

// ✅ Styled TableCell with animation
const AnimatedTableCell = styled(TableCell)(({ theme }) => ({
  transition: 'all 0.3s ease-in-out',
  overflow: 'hidden',
  whiteSpace: 'nowrap',
  '&.hidden': {
    width: 0,
    padding: 0,
    opacity: 0,
    border: 'none',
  },
}));

const StudentTable: React.FC = () => {
  const [hideRollNumber, setHideRollNumber] = useState(false);

  const students = [
    { id: 1, name: '<PERSON>', roll: 'A101', amount: 500 },
    { id: 2, name: '<PERSON>', roll: 'A102', amount: 700 },
    { id: 3, name: '<PERSON>', roll: 'A103', amount: 600 },
  ];

  const toggleRollColumn = () => setHideRollNumber((prev) => !prev);

  return (
    <TableContainer component={Paper}>
      <Table>
        {/* Table Header */}
        <TableHead>
          <TableRow>
            <TableCell>ID</TableCell>
            <TableCell>Student Name</TableCell>
            <AnimatedTableCell className={hideRollNumber ? 'hidden' : ''}>
              Roll Number{' '}
              <Button size="small" variant="contained" onClick={toggleRollColumn}>
                {hideRollNumber ? 'Show' : 'Hide'}
              </Button>
            </AnimatedTableCell>
            <TableCell>Amount</TableCell>
          </TableRow>
        </TableHead>

        {/* Table Body */}
        <TableBody>
          {students.map((student) => (
            <TableRow key={student.id}>
              <TableCell>{student.id}</TableCell>
              <TableCell>{student.name}</TableCell>
              <AnimatedTableCell className={hideRollNumber ? 'hidden' : ''}>{student.roll}</AnimatedTableCell>
              <TableCell>{student.amount}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default StudentTable;
