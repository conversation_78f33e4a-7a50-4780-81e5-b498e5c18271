/* eslint-disable no-nested-ternary */
import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, Outlet } from 'react-router-dom';
import Page from '@/components/shared/Page';
import { Box, ToggleButtonGroup, ToggleButton, Typography, useTheme } from '@mui/material';
import styled from 'styled-components';

const FeeListGroupRoot = styled.div`
  padding: 0.5rem 1rem 1rem 1rem;
  @media screen and (max-width: 576px) {
    padding: 0.5rem;
  }

  .toggle-container {
    padding-bottom: 0.5rem;
    padding-left: 0.5rem;
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[100] : props.theme.palette.grey[900]};
    border-radius: 8px;
  }

  .content-container {
    /* Remove Page wrapper padding since components have their own */
    margin: -1rem;
    @media screen and (max-width: 576px) {
      margin: -0.5rem;
    }
  }
`;

type FeeType = 'basic' | 'term';

function FeeListGroup() {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const [feeType, setFeeType] = useState<FeeType>('basic');

  // Sync URL with fee type selection
  useEffect(() => {
    const currentPath = location.pathname;
    if (currentPath.includes('/fee-lists/term')) {
      setFeeType('term');
    } else if (currentPath.includes('/fee-lists/basic')) {
      setFeeType('basic');
    } else if (currentPath === '/manage-fee/fee-lists') {
      // Only navigate if we're exactly on the parent route
      setFeeType('basic');
      navigate('/manage-fee/fee-lists/basic', { replace: true });
    }
  }, [location.pathname, navigate]);

  const handleFeeTypeChange = (_event: React.MouseEvent<HTMLElement>, newFeeType: FeeType | null) => {
    if (newFeeType !== null) {
      setFeeType(newFeeType);
      // Update URL based on selection
      if (newFeeType === 'basic') {
        navigate('/manage-fee/fee-lists/basic', { replace: true });
      } else {
        navigate('/manage-fee/fee-lists/term', { replace: true });
      }
    }
  };
  return (
    <Page title="Fee List Groups">
      <FeeListGroupRoot>
        <div className="toggle-container">
          <Box display="flex" flexDirection="row" alignItems="center" justifyContent='center' gap={2}>
            <Typography
              variant="h6"
              fontSize={18}
              fontWeight={600}
              color={theme.palette.mode === 'light' ? 'inherit' : 'text.primary'}
            >
              Fee Management
            </Typography>
            <ToggleButtonGroup
              value={feeType}
              exclusive
              onChange={handleFeeTypeChange}
              aria-label="Fee Type Selection"
              size="small"
              sx={{
                '& .MuiToggleButton-root': {
                  px: 3,
                  py: 1,
                  fontWeight: 600,
                  borderRadius: '20px',
                  border: `2px solid ${theme.palette.primary.main}`,
                  color: theme.palette.primary.main,
                  '&.Mui-selected': {
                    backgroundColor: theme.palette.primary.main,
                    color: theme.palette.primary.contrastText,
                    '&:hover': {
                      backgroundColor: theme.palette.primary.dark,
                    },
                  },
                  '&:hover': {
                    backgroundColor: theme.palette.primary.light,
                    color: theme.palette.primary.contrastText,
                  },
                },
              }}
            >
              <ToggleButton value="basic">Fee Wise</ToggleButton>
              <ToggleButton value="term">Term Wise</ToggleButton>
            </ToggleButtonGroup>
          </Box>
        </div>

        <div className="content-container">
          <Outlet />
        </div>
      </FeeListGroupRoot>
    </Page>
  );
}

export default React.memo(FeeListGroup);
