/* eslint-disable no-nested-ternary */
/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable no-unsafe-optional-chaining */
/* eslint-disable jsx-a11y/alt-text */
import Page from '@/components/shared/Page';
import { TOP_BAR_HEIGHT } from '@/config/Constants';
import {
  Box,
  Checkbox,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Table,
  TableContainer,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Typography,
  Card,
  Avatar,
  Tooltip,
  IconButton,
  MenuItem,
  Chip,
  Collapse,
  FormControl,
  Select,
  SelectChangeEvent,
  Snackbar,
  Alert,
} from '@mui/material';
import styled, { useTheme } from 'styled-components';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import { FormEvent, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import useSettings from '@/hooks/useSettings';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getClassSectionsData,
  getManageFeeclassListData,
  getYearData,
  getstudentTermFeeStatusNewListData,
  getstudentTermFeeStatusNewListStatus,
  getstudentsFeeStatusListData,
} from '@/config/storeSelectors';
import { StudentFeeStatusDataType, StudentTermFeeDetailsType, StudentTermFeePayType } from '@/types/ManageFee';
import {
  CheckReceiptNo,
  fetchClassList,
  fetchClassSections,
  fetchStudentTermFeeStatusNew,
  fetchStudentsFeeStatus,
} from '@/store/ManageFee/manageFee.thunks';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import DatePickers from '@/components/shared/Selections/DatePicker';
import dayjs from 'dayjs';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import { FEE_TYPE_ID_OPTIONS } from '@/config/Selection';
import StudentsPickerField from '@/components/shared/Selections/StudentsPicker';
import man from '@/assets/man.png';
import woman from '@/assets/woman.png';
import { Pay } from './PayDrawer';

const FeesDetailsRoot = styled.div`
  /* min-height: calc(100vh - ${TOP_BAR_HEIGHT}); */
  padding: 1rem;
  @media screen and (max-width: 576px) {
    padding: 0.5rem;
  }

  .Card {
    /* height: calc(100vh - 160px); */

    /* @media screen and (max-width: 996px) {
      height: 100%;
    } */

    .card-main-body {
      display: flex;
      flex-direction: column;
      height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        /* max-height: calc(100% - 50px); */
        max-height: 463px;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid #e8e8e9;
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
        .MuiPaper-root {
          border-bottom-left-radius: 0px;
          border-bottom-right-radius: 0px;
        }
      }
    }
    .Payment_Info .MuiTableCell-root.MuiTableCell-head {
      background-color: ${(props) =>
        props.theme.themeMode === 'light' ? props.theme.palette.grey[100] : props.theme.palette.grey[900]};
      /* border-top-right-radius: 0px;
      border-top-left-radius: 0px; */
    }
    /* .Payment_Info .MuiTableCell-root:first-of-type {
      border-top-left-radius: 0px;
      background-color: red;
    } */
    .Payment_Info .MuiTableCell-root {
      font-size: 12px;
    }
    .lateFee_info .MuiTableCell-root {
      font-size: 12px;
    }
    .Payment_Info .MuiTableCell-root {
      border: 0px;
      border-bottom: 0px;
    }
    .lateFee_info .MuiTableCell-root.MuiTableCell-head {
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
    }
  }
`;
interface StudentTermFeeDetailswithCheckedType extends StudentTermFeeDetailsType {
  checked: boolean;
}
type FeeDetailsProps = {
  student?: StudentFeeStatusDataType;
  onBackClick?: () => void;
};

const EllipsisWithTooltip = ({ text }: { text: string }) => {
  const textRef = useRef<HTMLDivElement>(null);
  const [isOverflowed, setIsOverflowed] = useState(false);

  useEffect(() => {
    const el = textRef.current;
    if (el) {
      setIsOverflowed(el.scrollWidth > el.clientWidth);
    }
  }, [text]);

  return (
    <Tooltip title={isOverflowed ? text : ''} disableHoverListener={!isOverflowed}>
      <Box
        ref={textRef}
        component="span"
        sx={{
          display: 'inline-block',
          width: isOverflowed ? '140px' : 'fit-content',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          verticalAlign: 'bottom',
        }}
      >
        {text}
      </Box>
    </Tooltip>
  );
};

export default function FeesDetails({ student, onBackClick }: FeeDetailsProps) {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const { user } = useAuth();
  const dispatch = useAppDispatch();
  const adminId: number = user ? user.accountId : 0;
  const [showFilter, setShowFilter] = useState(true);
  const [totalFee, setTotalFee] = useState(0);
  const defaultPaymentRows = [
    {
      id: 1,
      paymentTypeId: 0,
      amount: '',
      chequeOrDDNo: '',
      dateOfIssue: '',
      deppositDate: '',
      clearingDate: '',
      bankId: 0,
      payStatus: '',
    },
  ];
  const [rows, setRows] = useState(defaultPaymentRows);
  const [sendReq, setSendReq] = useState<StudentTermFeePayType>({});
  const [receiptType, setReceiptType] = useState('');

  const YearData = useAppSelector(getYearData);
  const defaultYear = YearData[0]?.accademicId || 0;
  const [academicYearFilter, setAcademicYearFilter] = useState(defaultYear);
  const [feeTypeFilter, setFeeTypeFilter] = useState(0);
  const [classSectionsFilter, setClassSectionsFilter] = useState(student?.sectionId || 0);
  const [classFilter, setClassFilter] = useState(student?.classId || 0);
  const [studentFilter, setStudentFilter] = useState(student?.studentId || 0);
  const ClassSectionsData = useAppSelector(getClassSectionsData);
  const [classListData, setClassListData] = useState(useAppSelector(getManageFeeclassListData));
  const [studentsListData, setStudentsListData] = useState(useAppSelector(getstudentsFeeStatusListData));
  const [studentSelected, setStudentSelected] = useState(student);
  const [remarks, setRemarks] = useState('');
  const [payingDate, setPayingDate] = useState('');
  const [payingReceiptNo, setPayingReceiptNo] = useState('');
  // const [selectedTerms, setSelectedTerms] = useState<StudentTermFeeDetailsType[]>([]);
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [errorInput, setErrorInput] = useState(false);
  const [errorInputTotal, setErrorInputTotal] = useState(false);
  const [error, setError] = useState(false);
  const [snackbarOpen, setSnackbarOpen] = useState(false);

  const studentsFeeStatusListData = useAppSelector(getstudentTermFeeStatusNewListData)?.termFeeDetails;
  const studentTermFeeStatusNewListData = useAppSelector(getstudentTermFeeStatusNewListData);
  const paymentTypesData = studentTermFeeStatusNewListData?.paymentTypes;
  const bankDetailsData = studentTermFeeStatusNewListData?.bankDetails;
  const studentsFeeStatusListStatus = useAppSelector(getstudentTermFeeStatusNewListStatus);
  const [selectedStudentIds, setSelectedStudentIds] = useState<string>('');
  const [selectedStudentData, setSelectedStudentData] = useState<any[]>([]);

  const [feeDetailsData, setFeeDetailsData] = useState<StudentTermFeeDetailswithCheckedType[]>([]);
  const [checked, setChecked] = useState<0 | 1 | 2>(0);
  const [isCollapsed, setIsCollapsed] = useState(false);

  const handleToggleView = () => {
    setIsCollapsed((prev) => !prev);
  };
  // setFeeDetailsData(studentsFeeStatusListData);
  const currentFeeDetailsRequest = useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      sectionId: classSectionsFilter,
      classId: classFilter,
      studentId: studentFilter,
      feeTypeId: feeTypeFilter,
    }),
    [adminId, academicYearFilter, classSectionsFilter, classFilter, studentFilter, feeTypeFilter]
  );

  const loadFeeDetailsList = useCallback(
    async (request: {
      adminId: number;
      academicId: number;
      sectionId: number;
      classId: number;
      studentId: number;
      feeTypeId: number;
    }) => {
      try {
        setErrorInput(false);
        setErrorInputTotal(false);
        setRemarks('');
        setPayingDate('');
        setPayingReceiptNo('');
        setRows(defaultPaymentRows);
        setTotalFee(0);
        const data = await dispatch(fetchStudentTermFeeStatusNew(request)).unwrap();
        // console.log('data::::', data);
        setFeeDetailsData(
          data.termFeeDetails.map((item) => {
            return { ...item, checked: false };
          })
        );
        setReceiptType(
          typeof data.receiptPrintType === 'object' && data.receiptPrintType !== null
            ? data.receiptPrintType.receiptType
            : data.receiptPrintType
        );
      } catch (error) {
        console.error('Error loading term fee list:', error);
      }
    },
    [dispatch]
  );
  const loadClassListData = useCallback(
    async (request: { adminId: number; academicId: number; sectionId: number }) => {
      try {
        const data = await dispatch(fetchClassList(request)).unwrap();
        setClassListData(data);
      } catch (error) {
        console.error('Error loading term fee list:', error);
      }
    },
    [dispatch]
  );
  const loadStudentsListData = useCallback(
    async (request: { adminId: number; academicId: number; sectionId: number; classId: number }) => {
      try {
        const data = await dispatch(fetchStudentsFeeStatus(request)).unwrap();
        setStudentsListData(data);
        setFeeDetailsData((preData) => {
          return preData.map((item) => {
            return { ...item, checked: false };
          });
        });
      } catch (error) {
        console.error('Error loading term fee list:', error);
      }
    },
    [dispatch]
  );

  const FindTotalFee = (selectedRows: StudentTermFeeDetailswithCheckedType[]) => {
    const total = selectedRows?.reduce((accum: number, item) => accum + item.termBalance, 0);
    setTotalFee(total);
    if (rows.length === 1) {
      setRows([
        {
          id: 1,
          paymentTypeId: paymentTypesData ? paymentTypesData[0].paymentTypeId : 0,
          amount: total.toString(),
          chequeOrDDNo: '',
          dateOfIssue: '',
          deppositDate: '',
          clearingDate: '',
          bankId: bankDetailsData ? bankDetailsData[0].bankId : 0,
          payStatus: '',
        },
      ]);
    }
  };

  useEffect(() => {
    dispatch(fetchYearList(adminId));
    dispatch(fetchClassSections({ adminId, academicId: academicYearFilter }));
    console.log('EfeeTypeFilter::::----', feeTypeFilter);
    // dispatch(fetchClassList({ adminId, academicId: academicYearFilter, sectionId: classSectionsFilter }));
    // dispatch(
    //   fetchStudentsFeeStatus({
    //     adminId,
    //     academicId: academicYearFilter,
    //     sectionId: classSectionsFilter,
    //     classId: classFilter,
    //   })
    // );
    // dispatch(fetchClassList({ adminId, academicId, sectionId: classSectionsFilter }));
    if (feeDetailsData.length === 0 && student) {
      loadFeeDetailsList(currentFeeDetailsRequest);
    }
  }, [adminId, currentFeeDetailsRequest, dispatch, feeDetailsData.length, loadFeeDetailsList, student]);

  useEffect(() => {
    const payableRows = feeDetailsData.filter(
      (row) =>
        row.termBalance !== 0 && row.termBalance - (row.scholarship || 0) - (row.discount || 0) + (row.fine || 0) > 0
    );

    const selected = payableRows.filter((row) => row.checked).length;

    if (selected === 0) {
      // ✅ None selected
      setChecked(0);
    } else if (selected === payableRows.length) {
      // ✅ All payable rows selected
      setChecked(1);
    } else {
      // ✅ Some rows selected
      setChecked(2);
    }
  }, [feeDetailsData]);

  const handleClose = () => {
    setDrawerOpen(false);
    setFeeDetailsData((preData) => {
      return preData.map((item) => {
        return { ...item, checked: false };
      });
    });
    setRemarks('');
    setPayingDate('');
    setPayingReceiptNo('');
    setRows(defaultPaymentRows);
    setTotalFee(0);
  };
  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setAcademicYearFilter(0);
      setClassSectionsFilter(0);
      setClassFilter(0);
      setStudentFilter(0);
      handleClose();
      setStudentSelected({});
      setSelectedStudentData([]);
      setSelectedStudentIds('');
      setFeeTypeFilter(0);
      loadFeeDetailsList({
        adminId,
        feeTypeId: 0,
        academicId: 0,
        sectionId: 0,
        classId: 0,
        studentId: 0,
      });
      // dispatch(fetchFeeDateSettings({ adminId, academicId: 10, sectionId: 0 }));
    },
    [adminId, loadFeeDetailsList]
  );

  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId);
    // loadFeeDetailsList({ ...currentFeeDetailsRequest, academicId: parseInt(e.target.value, 10) });
    setClassFilter(0);
    setClassSectionsFilter(0);
    setStudentFilter(0);
  };

  const handleFeeTypeChange = (e: SelectChangeEvent) => {
    const feeTypeId = parseInt(e.target.value, 10);
    setFeeTypeFilter(feeTypeId);
    loadFeeDetailsList({ ...currentFeeDetailsRequest, feeTypeId: parseInt(e.target.value, 10) });
  };

  const handleClassSectionChange = (e: SelectChangeEvent) => {
    setClassSectionsFilter(
      parseInt(ClassSectionsData.filter((item) => item.sectionId === e.target.value)[0].sectionId, 10)
    );
    const { classId, studentId, ...rest } = currentFeeDetailsRequest;
    loadClassListData({ ...rest, sectionId: parseInt(e.target.value, 10) });
    // loadFeeDetailsList({ ...currentFeeDetailsRequest, sectionId: parseInt(e.target.value, 10) });
    setClassFilter(0);
    setStudentFilter(0);
  };

  const handleClassChange = (e: SelectChangeEvent) => {
    setClassFilter(parseInt(classListData.filter((item) => item.classId === e.target.value)[0].classId, 10));
    const { studentId, ...rest } = currentFeeDetailsRequest;
    loadStudentsListData({ ...rest, classId: parseInt(e.target.value, 10) });
    // loadFeeDetailsList({ ...currentFeeDetailsRequest, classId: parseInt(e.target.value, 10) });
    setStudentFilter(0);
  };

  const handleStudentChange = (e: SelectChangeEvent) => {
    setStudentFilter(studentsListData.filter((item) => item.studentId === parseInt(e.target.value, 10))[0].studentId);
    loadFeeDetailsList({ ...currentFeeDetailsRequest, studentId: parseInt(e.target.value, 10) });
    setStudentSelected(studentsListData.filter((item) => item.studentId === parseInt(e.target.value, 10))[0]);
    console.log('student::::----', studentFilter);
    console.log('studentSelected::::----', studentSelected);
  };

  type FieldType = 'dateOfIssue' | 'deppositDate' | 'clearingDate';
  const handleDateChange = (date, row, fieldName: FieldType) => {
    const formattedDate = date ? dayjs(date).format('DD/MM/YYYY') : '';
    setRows((prevItems) => {
      const newRows = [...prevItems];
      newRows[row.id - 1][fieldName] = formattedDate;
      return newRows;
    });
  };
  const handlePayingDateChange = (date) => {
    const formattedDate = date ? dayjs(date).format('DD/MM/YYYY') : '';
    // console.log(formattedDate);
    setPayingDate(formattedDate);
  };

  const handleAddRow = () => {
    const newRow = {
      id: rows.length + 1,
      paymentTypeId: 0,
      amount: '',
      chequeOrDDNo: '',
      dateOfIssue: '',
      deppositDate: '',
      clearingDate: '',
      bankId: 0,
      payStatus: '',
    };
    setRows([...rows, newRow]);
  };

  const handleDeleteRow = (id: number) => {
    if (id === 1) {
      return;
    }
    const updatedRows = rows.filter((row) => row.id !== id);
    setRows(updatedRows);
  };

  const checkReceiptNumber = useCallback(
    async (receiptNo: number) => {
      console.log('receiptNo::::----', receiptNo);
      try {
        const response = await dispatch(CheckReceiptNo({ receiptNo, feeTypeId: feeTypeFilter })).unwrap();
        console.log('response::::----', response);

        if (response) {
          if (response.result === 'Exist') {
            setError(true);
            setSnackbarOpen(true);
          }
        } else {
          setError(false);
        }
      } catch {
        console.error('Error checking receipt number:');
      }
    },
    [dispatch, feeTypeFilter]
  );
  const handleClickOutside = useCallback(() => {
    if (payingReceiptNo) {
      checkReceiptNumber(parseInt(payingReceiptNo, 10));
    }
  }, [checkReceiptNumber, payingReceiptNo]);

  // Handle click outside TextField
  useEffect(() => {
    const handleClick = (e) => {
      const receiptNoElement = document.getElementById('receiptNumber');
      console.log('receiptNoElement::::----', receiptNoElement);

      if (receiptNoElement && !receiptNoElement.contains(e.target)) {
        handleClickOutside();
      }
    };

    document.addEventListener('click', handleClick);

    return () => {
      document.removeEventListener('click', handleClick);
    };
  }, [handleClickOutside]);

  const handleSave = useCallback(async () => {
    setSendReq({
      adminId,
      academicId: academicYearFilter || 0,
      sectionId: classSectionsFilter || 0,
      classId: classFilter || 0,
      studentId: studentFilter || 0,
      totalAmount: totalFee,
      grandTotal: totalFee,
      feeTypeId: feeTypeFilter,
      totalScholarship: feeDetailsData
        .filter((item) => item.checked === true)
        .reduce((accum: number, item) => accum + item.scholarship, 0),
      totalDiscount: feeDetailsData
        .filter((item) => item.checked === true)
        ?.reduce((accum: number, item) => accum + item.discount, 0),
      totalFine: feeDetailsData
        .filter((item) => item.checked === true)
        ?.reduce((accum: number, item) => accum + item.fine, 0),
      payStatus: '',
      receiptNo: 0,
      termfeepaying: feeDetailsData
        .filter((item) => item.checked === true)
        .map((item) => ({
          feeId: item.feeId,
          termId: item.termId,
          payStatus: '',
          receiptDetailsId: 0,
          totalAmount: item.termBalance,
          scholarship: item.scholarship,
          discount: item.discount,
          fine: item.fine,
          grandTotal: item.termBalance,
        })),
      paymentmode: rows.map((item) => {
        const { id, amount, ...rest } = item;
        const amt = parseInt(amount, 10);
        return { amount: amt, ...rest };
      }),
      remarks,
      payingDate,
      payingReceiptNo: payingReceiptNo === '' ? 0 : parseInt(payingReceiptNo, 10),
      receiptPrintType: { receiptType },
    });
    setDrawerOpen(true);
  }, [
    academicYearFilter,
    adminId,
    classFilter,
    classSectionsFilter,
    payingDate,
    remarks,
    rows,
    studentFilter,
    totalFee,
    feeTypeFilter,
  ]);

  // const handleRowClick = (row: StudentTermFeeDetailswithCheckedType) => {
  //   const updatedRows = feeDetailsData.map((item) => {
  //     if (item.feeId === row.feeId && item.termId === row.termId) {
  //       if (item.checked) {
  //         return {
  //           ...item,
  //           checked: false,
  //           termBalance: studentsFeeStatusListData
  //             ? studentsFeeStatusListData?.filter((prev) => prev.feeId === item.feeId && prev.termId === item.termId)[0]
  //                 .termBalance
  //             : item.termBalance,
  //         };
  //       }
  //       return { ...item, checked: true };
  //     }
  //     return item;
  //   });
  //   setFeeDetailsData(updatedRows);
  //   FindTotalFee(updatedRows.filter((item) => item.checked === true));
  // };

  const handleRowClick = (row: StudentTermFeeDetailswithCheckedType) => {
    const updatedRows = feeDetailsData.map((item) => {
      if (item.feeId === row.feeId && item.termId === row.termId) {
        if (item.checked) {
          // Uncheck and restore original termBalance
          const originalBalance =
            studentsFeeStatusListData?.find((prev) => prev.feeId === item.feeId && prev.termId === item.termId)
              ?.termBalance ?? item.termBalance;
          return {
            ...item,
            checked: false,
            termBalance: originalBalance,
          };
        } else {
          // Check and recalculate termBalance including scholarship, discount, and fine
          const newTermBalance = Math.max(
            item.termBalance - (item.scholarship || 0) - (item.discount || 0) + (item.fine || 0),
            0
          );
          return {
            ...item,
            checked: true,
            termBalance: newTermBalance,
          };
        }
      }
      return item;
    });

    setFeeDetailsData(updatedRows);
    FindTotalFee(updatedRows.filter((item) => item.checked));
  };

  // const handleRowAllClick = () => {
  //   if (checked === 1) {
  //     const updatedRows = feeDetailsData.map((item) => {
  //       if (item.termBalance !== 0) {
  //         return {
  //           ...item,
  //           checked: false,
  //           termBalance: studentsFeeStatusListData
  //             ? studentsFeeStatusListData?.filter((prev) => prev.feeId === item.feeId && prev.termId === item.termId)[0]
  //                 .termBalance
  //             : item.termBalance,
  //         };
  //       }
  //       return item;
  //     });
  //     setFeeDetailsData(updatedRows);
  //     FindTotalFee(updatedRows.filter((item) => item.checked === true));
  //   } else {
  //     const updatedRows = feeDetailsData.map((item) => {
  //       if (item.termBalance !== 0) {
  //         return { ...item, checked: true };
  //       }
  //       return item;
  //     });
  //     setFeeDetailsData(updatedRows);
  //     FindTotalFee(updatedRows.filter((item) => item.checked === true));
  //   }
  // };

  const handleRowAllClick = () => {
    if (checked === 1) {
      // ✅ Uncheck ALL rows and restore original balances
      const updatedRows = feeDetailsData.map((item) => {
        const originalBalance =
          studentsFeeStatusListData?.find((prev) => prev.feeId === item.feeId && prev.termId === item.termId)
            ?.termBalance ?? item.termBalance;

        return {
          ...item,
          checked: false,
          termBalance: originalBalance,
        };
      });

      setFeeDetailsData(updatedRows);
      FindTotalFee([]);
      setChecked(0); // ✅ ensure header checkbox updates to unchecked
    } else {
      // ✅ Check only rows where payable > 0
      const updatedRows = feeDetailsData.map((item) => {
        const calculatedPayable = item.termBalance - (item.scholarship || 0) - (item.discount || 0) + (item.fine || 0);

        if (calculatedPayable > 0) {
          return {
            ...item,
            checked: true,
            termBalance: Math.max(calculatedPayable, 0),
          };
        }

        return { ...item, checked: false };
      });

      setFeeDetailsData(updatedRows);
      FindTotalFee(updatedRows.filter((item) => item.checked));
      setChecked(1); // ✅ update header checkbox to checked
    }
  };

  const getRowKey = useCallback((row: StudentTermFeeDetailswithCheckedType) => `${row.feeId}_${row.termId}`, []);

  const currentDate = dayjs();

  const feeDetailsListColumns: DataTableColumn<StudentTermFeeDetailswithCheckedType>[] = useMemo(
    () => [
      {
        name: 'selection',
        renderHeader: () => {
          return (
            <Checkbox
              checked={checked === 1}
              indeterminate={checked === 2}
              onClick={() => {
                handleRowAllClick();
              }}
            />
          );
        },
        renderCell: (row) => {
          return (
            <Checkbox
              checked={row.checked}
              // disabled={row.termBalance === 0}
              disabled={
                row.termBalance === 0 ||
                row.termBalance - (row.scholarship || 0) - (row.discount || 0) + (row.fine || 0) <= 0
              }
              // disabled={
              //   studentsFeeStatusListData?.filter((prev) => prev.feeId === row.feeId && prev.termId === row.termId)[0]
              //     .termBalance === 0
              // }
              onClick={() => {
                handleRowClick(row);
              }}
            />
          );
        },
      },
      {
        name: 'feeHead',
        renderHeader: () => {
          return <Typography variant="subtitle2">Fee Head</Typography>;
        },
        renderCell: (row) => {
          return (
            <Typography variant="subtitle2" color={isLight ? theme.palette.grey[700] : 'inherit'} fontSize={13}>
              {row.feeTitle}
            </Typography>
          );
        },
      },
      {
        name: 'feePeriod',
        headerLabel: 'Fee Period',
        renderCell: (row) => {
          return (
            <Chip
              size="small"
              variant="outlined"
              sx={{
                p: 0.5,
                borderRadius: 1,
                minWidth: 60,
                backgroundColor: isLight ? theme.palette.grey[300] : theme.palette.grey[400],
              }}
              label={row.termTitle}
            />
          );
        },
      },
      {
        name: 'amount',
        renderHeader: () => {
          return <Typography variant="subtitle2">Amount</Typography>;
        },
        dataKey: 'termAmount',
      },
      // {
      //   name: 'Scholarship',
      //   renderHeader: () => {
      //     return <Typography variant="subtitle2">Scholarship</Typography>;
      //   },
      //   dataKey: 'scholarship',
      // },
      // {
      //   name: 'discount',
      //   renderHeader: () => {
      //     return <Typography variant="subtitle2">Discount</Typography>;
      //   },
      //   dataKey: 'discount',
      // },
      // {
      //   name: 'fine',
      //   renderHeader: () => {
      //     return <Typography variant="subtitle2">Fine</Typography>;
      //   },
      //   dataKey: 'fine',
      // },
      {
        name: 'Scholarship',
        renderHeader: () => <Typography variant="subtitle2">Scholarship</Typography>,
        renderCell: (row) =>
          row.checked ? (
            <TextField
              type="number"
              size="small"
              value={row.scholarship === 0 ? '' : row.scholarship}
              onChange={(e) => {
                const value = Number(e.target.value) || 0;
                const newData = feeDetailsData.map((item) => {
                  if (item.feeId === row.feeId && item.termId === row.termId) {
                    // Update scholarship and recalculate termBalance
                    const newTermBalance = Math.max(item.termAmount - value - item.discount + item.fine, 0);
                    return { ...item, scholarship: value, termBalance: newTermBalance };
                  }
                  return item;
                });
                setFeeDetailsData(newData);
                FindTotalFee(newData.filter((item) => item.checked));
              }}
              inputProps={{ style: { textAlign: 'right', fontSize: 13, padding: '5px 10px' } }}
              sx={{ width: 70 }}
            />
          ) : (
            <Typography variant="subtitle1" fontSize={13}>
              {row.scholarship}
            </Typography>
          ),
      },
      {
        name: 'discount',
        renderHeader: () => <Typography variant="subtitle2">Discount</Typography>,
        renderCell: (row) =>
          row.checked ? (
            <TextField
              type="number"
              size="small"
              value={row.discount === 0 ? '' : row.discount}
              onChange={(e) => {
                const value = Number(e.target.value) || 0;
                const newData = feeDetailsData.map((item) => {
                  if (item.feeId === row.feeId && item.termId === row.termId) {
                    // Update discount and recalculate termBalance
                    const newTermBalance = Math.max(item.termAmount - item.scholarship - value + item.fine, 0);
                    return { ...item, discount: value, termBalance: newTermBalance };
                  }
                  return item;
                });
                setFeeDetailsData(newData);
                FindTotalFee(newData.filter((item) => item.checked));
              }}
              inputProps={{ style: { textAlign: 'right', fontSize: 13, padding: '5px 10px' } }}
              sx={{ width: 70 }}
            />
          ) : (
            <Typography variant="subtitle1" fontSize={13}>
              {row.discount}
            </Typography>
          ),
      },
      {
        name: 'fine',
        renderHeader: () => <Typography variant="subtitle2">Fine</Typography>,
        renderCell: (row) =>
          row.checked ? (
            <TextField
              type="number"
              size="small"
              value={row.fine === 0 ? '' : row.fine}
              onChange={(e) => {
                const value = Number(e.target.value) || 0;
                const newData = feeDetailsData.map((item) => {
                  if (item.feeId === row.feeId && item.termId === row.termId) {
                    // Update fine and recalculate termBalance
                    const newTermBalance = Math.max(item.termAmount - item.scholarship - item.discount + value, 0);
                    return { ...item, fine: value, termBalance: newTermBalance };
                  }
                  return item;
                });
                setFeeDetailsData(newData);
                FindTotalFee(newData.filter((item) => item.checked));
              }}
              inputProps={{ style: { textAlign: 'right', fontSize: 13, padding: '5px 10px' } }}
              sx={{ width: 70 }}
            />
          ) : (
            <Typography variant="subtitle1" fontSize={13}>
              {row.fine}
            </Typography>
          ),
      },
      {
        name: 'paid',
        renderHeader: () => {
          return (
            <Typography variant="subtitle2" textAlign="right">
              Fees Paid
            </Typography>
          );
        },
        renderCell: (row) => {
          return (
            <Typography
              color="success"
              // fontSize={13}
              sx={{ color: theme.palette.success.main }}
              variant="subtitle2"
              textAlign="right"
            >
              {row.termPaid}
            </Typography>
          );
        },
      },
      {
        name: 'balance',
        renderHeader: () => {
          return (
            <Typography variant="subtitle2" textAlign="right">
              Payable Fee
            </Typography>
          );
        },
        align: 'end',
        renderCell: (row) => {
          return row.checked ? (
            <TextField
              inputProps={{
                style: {
                  textAlign: 'right',
                  padding: '5px 10px',
                },
              }}
              error={errorInput}
              helperText={errorInput && 'Enter a Valid Input'}
              FormHelperTextProps={{
                sx: {
                  margin: 0,
                  fontSize: '9px',
                },
              }}
              value={row.termBalance.toString()}
              // onChange={(e) => {
              //   const value = parseInt(e.target.value, 10);
              //   if (
              //     value >
              //     (studentsFeeStatusListData
              //       ? studentsFeeStatusListData?.filter(
              //           (item) => item.feeId === row.feeId && item.termId === row.termId
              //         )[0].termBalance
              //       : row.termBalance)
              //   ) {
              //     setErrorInput(true);
              //     setErrorInputTotal(true);
              //   } else if (
              //     value <=
              //     (studentsFeeStatusListData
              //       ? studentsFeeStatusListData?.filter(
              //           (item) => item.feeId === row.feeId && item.termId === row.termId
              //         )[0].termBalance
              //       : row.termBalance)
              //   ) {
              //     setErrorInput(false);
              //     setErrorInputTotal(false);
              //   }
              //   const newData = feeDetailsData.map((item) => {
              //     if (item.feeId === row.feeId && item.termId === row.termId) {
              //       if (e.target.value === '') {
              //         return { ...item, termBalance: 0 };
              //       }
              //       return { ...item, termBalance: value };
              //     }
              //     return item;
              //   });
              //   setFeeDetailsData(newData);
              //   FindTotalFee(newData.filter((item) => item.checked === true));
              // }}
              onChange={(e) => {
                const inputValue = e.target.value === '' ? 0 : parseInt(e.target.value, 10);
                const originalMax =
                  studentsFeeStatusListData?.find((item) => item.feeId === row.feeId && item.termId === row.termId)
                    ?.termBalance ?? row.termBalance;

                // Validation
                if (inputValue > originalMax) {
                  setErrorInput(true);
                  setErrorInputTotal(true);
                } else {
                  setErrorInput(false);
                  setErrorInputTotal(false);
                }

                const newData = feeDetailsData.map((item) => {
                  if (item.feeId === row.feeId && item.termId === row.termId) {
                    // Apply formula considering Scholarship, Discount, and Fine
                    // const newTermBalance = Math.max(
                    //   inputValue - (item.scholarship || 0) - (item.discount || 0) + (item.fine || 0),
                    //   0
                    // );
                    return { ...item, termBalance: inputValue };
                  }
                  return item;
                });

                setFeeDetailsData(newData);
                FindTotalFee(newData.filter((item) => item.checked));
              }}
              sx={{ width: 85 }}
            />
          ) : (
            <Typography variant="subtitle2">
              {/* {row.termPaid > row.termAmount ? 0 : row.termBalance} */}
              {/* {row.termBalance - (row.scholarship || 0) - (row.discount || 0) + (row.fine || 0)} */}
              {Math.max(row.termBalance - (row.scholarship || 0) - (row.discount || 0) + (row.fine || 0), 0)}

              {/* {row.termBalance < row.termBalance - (row.scholarship || 0) - (row.discount || 0) + (row.fine || 0)
                ? 0
                : row.termBalance - (row.scholarship || 0) - (row.discount || 0) + (row.fine || 0)} */}
            </Typography>
          );
        },
      },
    ],
    [feeDetailsData, handleRowClick, isLight, theme.palette.grey, theme.palette.success.main, studentsFeeStatusListData]
  );

  return (
    <Page title="Fees Reciepts">
      <FeesDetailsRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Box flexWrap="wrap" rowGap={2} display="flex" py={0.5} justifyContent="space-between" alignItems="center">
            <Stack gap={2} direction="row" alignItems="center">
              {student && (
                <Tooltip title="Back">
                  <IconButton
                    onClick={onBackClick}
                    sx={{
                      backgroundColor: theme.palette.secondary.main,
                      color: theme.palette.common.white,
                      '&:hover': { backgroundColor: theme.palette.secondary.dark },
                      width: '25px',
                      height: '25px',
                    }}
                    size="small"
                  >
                    <ArrowBackIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
              <Typography variant="h6" fontSize={17}>
                Pay Individual Student Fees
              </Typography>
            </Stack>
            <Stack direction="row" gap={2} flexWrap="wrap">
              {/* <Stack direction="row" alignItems="center" gap={2}>
                <Typography fontSize={14} variant="subtitle1">
                  Receipt No
                </Typography>
                <TextField
                  inputProps={{
                    style: {
                      padding: '4px 10px',
                      width: 80,
                      backgroundColor: isLight ? theme.palette.grey[200] : theme.palette.grey[900],
                      borderRadius: 7,
                    },
                  }}
                  )
            <Typography variant="subtitle2" color="GrayText" textAlign="right">
              {row.termBalance}
            </Typography>
          );
        },
      },
    ],
    [isLight, theme.palette.grey, theme.palette.success.main]
  );
  return (
    <Page title="Fees Reciepts">
      <FeesDetailsRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Box flexWrap="wrap" rowGap={2} display="flex" py={0.5} justifyContent="space-between" alignItems="center">
            <Stack gap={2} direction="row" alignItems="center">
              {student && (
                <Tooltip title="Back">
                  <IconButton
                    onClick={onBackClick}
                    sx={{
                      backgroundColor: theme.palette.secondary.main,
                      color: theme.palette.common.white,
                      '&:hover': { backgroundColor: theme.palette.secondary.dark },
                      width: '25px',
                      height: '25px',
                    }}
                    size="small"
                  >
                    <ArrowBackIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
              <Typography variant="h6" fontSize={17}>
                Pay Individual Student Fees
              </Typography>
            </Stack>
            <Stack direction="row" gap={2} flexWrap="wrap">
              {/* <Stack direction="row" alignItems="center" gap={2}>
                <Typography fontSize={14} variant="subtitle1">
                  Receipt No
                </Typography>
                <TextField
                  inputProps={{
                    style: {
                      padding: '4px 10px',
                      width: 80,
                      backgroundColor: isLight ? theme.palette.grey[200] : theme.palette.grey[900],
                      borderRadius: 7,
                    },
                  }}
                  size="small"
                  id=""
                  label=""
                />
              </Stack>
              <Stack direction="row" alignItems="center" gap={2}>
                <Typography fontSize={14} variant="subtitle1">
                  Last Receipt No
                </Typography>
                <TextField
                  inputProps={{
                    style: {
                      padding: '4px 10px',
                      width: 80,
                      backgroundColor: isLight ? theme.palette.grey[200] : theme.palette.grey[900],
                      borderRadius: 7,
                    },
                  }}
                  size="small"
                  id=""
                  label=""
                />
              </Stack> */}
              <Box>
                {showFilter === false ? (
                  <Tooltip title="Search">
                    <IconButton
                      aria-label="delete"
                      color="primary"
                      sx={{ mr: { xs: 0, sm: 1 } }}
                      onClick={() => setShowFilter((x) => !x)}
                    >
                      <SearchIcon />
                    </IconButton>
                  </Tooltip>
                ) : (
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ mr: { xs: 0, sm: 1 } }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <IoIosArrowUp />
                  </IconButton>
                )}
              </Box>
            </Stack>
          </Box>
          <Divider sx={{ marginBottom: 1 }} />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={2} container spacing={2} alignItems="end">
                  <Grid item lg={2} md={6} sm={6} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter?.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg={2} md={6} sm={6} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Type
                      </Typography>
                      <Select
                        labelId="feeTypeFilter"
                        id="feeTypeFilterSelect"
                        value={feeTypeFilter.toString()}
                        onChange={handleFeeTypeChange}
                        placeholder="Select Year"
                      >
                        <MenuItem sx={{ display: 'none' }} value={0}>
                          Select Type
                        </MenuItem>
                        {FEE_TYPE_ID_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg={2} md={6} sm={6} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Section
                      </Typography>
                      <Select
                        labelId="classSectionsFilter"
                        id="classSectionsFilterSelect"
                        disabled={academicYearFilter === 0}
                        value={classSectionsFilter?.toString()}
                        onChange={handleClassSectionChange}
                        placeholder="Select Section"
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Section
                        </MenuItem>
                        {ClassSectionsData.map((opt) => (
                          <MenuItem key={opt.sectionId} value={opt.sectionId}>
                            {opt.sectionName}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg={2} md={6} sm={6} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Select
                        labelId="classFilter"
                        id="classFilter"
                        disabled={classSectionsFilter === 0}
                        value={classFilter?.toString()}
                        onChange={handleClassChange}
                        placeholder="Select Class"
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Class
                        </MenuItem>
                        {classListData.map((opt) => (
                          <MenuItem key={opt.classId} value={opt.classId}>
                            {opt.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  {/* <Grid item lg={2} md={6} sm={6} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Student
                      </Typography>

                      <StudentsPickerField
                        width="100%"
                        setSelectedStudentData={setSelectedStudentData}
                        componentsPropsWidth={350}
                        loadRecentPaidList={loadFeeDetailsList}
                        currentRecentPaidListRequest={currentFeeDetailsRequest}
                        setSelectedStudentIds={setSelectedStudentIds}
                        // classId={classFilter}
                        classId={-1}
                        academicId={academicYearFilter}
                        // multiple
                      />
                    </FormControl>
                  </Grid> */}
                  <Grid item lg={2} md={6} sm={6} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Student
                      </Typography>
                      <Select
                        labelId="studentFilter"
                        id="studentFilter"
                        disabled={classFilter === 0}
                        value={studentFilter?.toString()}
                        onChange={handleStudentChange}
                        placeholder="Select Student"
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Student
                        </MenuItem>
                        {studentsListData.map((opt) => (
                          <MenuItem key={opt.studentId} value={opt.studentId}>
                            {opt.studentName}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>

                  {/* <Grid item lg={2} md={6} sm={6} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Fee Template
                      </Typography>
                      <Select labelId="classStatusFilter" id="classStatusFilterSelect" value="">
                        <MenuItem value={-1}>All</MenuItem>
                        {STATUS_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item lg={2} md={6} sm={6} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Fee Date
                      </Typography>
                      <DatePickers name="messageDateFilter" value="" />
                    </FormControl>
                  </Grid>
                  <Grid item lg={2} md={6} sm={6} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Deposit upto
                      </Typography>
                      <Select labelId="classStatusFilter" id="classStatusFilterSelect" value="">
                        <MenuItem value={-1}>All</MenuItem>
                        {STATUS_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid> */}

                  <Grid item lg="auto">
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      {/* <Button type="submit" variant="contained" color="primary" fullWidth>
                        Search
                      </Button> */}
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>

            <Grid container spacing={2}>
              <Grid item xs={12} md={12} lg={isCollapsed ? 10 : 8} xl={isCollapsed ? 10 : 8}>
                <Paper className="card-table-container">
                  <DataTable
                    TableBodyHeight={265}
                    showHorizontalScroll
                    tableStyles={{ width: { xs: '800px', xxl: '100%' } }}
                    // ShowCheckBox
                    columns={feeDetailsListColumns}
                    // data={[
                    //   ...feeDetailsData?.filter((item) => item.termBalance === 0),
                    //   ...feeDetailsData?.filter((item) => item.termBalance !== 0),
                    // ]}
                    data={feeDetailsData}
                    getRowKey={getRowKey}
                    fetchStatus={studentsFeeStatusListStatus}
                    // RowSelected
                    // tableRowSelect
                    // onCheckboxClick={FindTotalFee}
                    // selectedRows={selectedTerms}
                    // setSelectedRows={setSelectedTerms}
                    // disabledCheckBox={feeDetailsData.filter((item) => item.termBalance === 0)}
                    footerColumn={
                      <TableRow
                        className="footer_row"
                        sx={{
                          position: 'sticky',
                          bottom: 0,
                          backgroundColor: isLight ? theme.palette.info.lighter : theme.palette.grey[900],
                        }}
                      >
                        <TableCell> </TableCell>
                        <TableCell>
                          <b>Total Amount</b>
                        </TableCell>
                        <TableCell> </TableCell>
                        <TableCell>
                          <b>{feeDetailsData?.reduce((accum: number, item) => accum + item.termAmount, 0)}</b>
                        </TableCell>
                        <TableCell>
                          <b>{feeDetailsData?.reduce((accum: number, item) => accum + item.scholarship, 0)}</b>
                        </TableCell>
                        <TableCell>
                          <b>{feeDetailsData?.reduce((accum: number, item) => accum + item.discount, 0)}</b>
                        </TableCell>
                        <TableCell>
                          <b>{feeDetailsData?.reduce((accum: number, item) => accum + item.fine, 0)}</b>
                        </TableCell>
                        <TableCell align="right">
                          <b>{feeDetailsData?.reduce((accum: number, item) => accum + item.termPaid, 0)}</b>
                        </TableCell>
                        <TableCell align="right">
                          <b>{feeDetailsData?.reduce((accum: number, item) => accum + item.termBalance, 0)}</b>
                        </TableCell>
                      </TableRow>
                    }
                  />
                  <Box py={1} px={2}>
                    <Typography variant="h6" fontSize={14}>
                      Total Amount Information:
                    </Typography>
                    <Grid container columnSpacing={2} rowSpacing={1} py={1.5}>
                      <Grid item xs={4} sm={2} lg={2} xl={2}>
                        <Stack direction="column" justifyContent="start" alignItems="start">
                          <Typography variant="subtitle1" fontSize={12} color="secondary">
                            Total
                          </Typography>
                          <TextField
                            fullWidth
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                '&:hover fieldset': {
                                  borderColor: isLight ? theme.palette.grey[400] : theme.palette.grey[700],
                                },
                                '&.Mui-focused fieldset': {
                                  borderColor: isLight ? theme.palette.grey[400] : theme.palette.grey[700],
                                },
                              },
                            }}
                            inputProps={{
                              readOnly: true,
                              style: {
                                borderRadius: 8,
                                textAlign: 'right',
                                padding: '5px 10px',
                              },
                            }}
                            FormHelperTextProps={{
                              sx: {
                                margin: 0,
                                fontSize: '9px',
                              },
                            }}
                            value={
                              // checked > 0
                              //   ?
                              feeDetailsData
                                .filter((item) => item.checked === true)
                                ?.reduce((accum: number, item) => accum + item.termAmount, 0)
                              // : feeDetailsData?.reduce((accum: number, item) => accum + item.termAmount, 0)
                            }
                          />
                        </Stack>
                      </Grid>
                      <Grid item xs={4} sm={2} lg={2} xl={2}>
                        <Stack direction="column" justifyContent="start" alignItems="start">
                          <Typography variant="subtitle1" fontSize={12} color="secondary">
                            Paid
                          </Typography>
                          <TextField
                            fullWidth
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                '&:hover fieldset': {
                                  borderColor: isLight ? theme.palette.grey[400] : theme.palette.grey[700],
                                },
                                '&.Mui-focused fieldset': {
                                  borderColor: isLight ? theme.palette.grey[400] : theme.palette.grey[700],
                                },
                              },
                            }}
                            inputProps={{
                              readOnly: true,
                              style: {
                                borderRadius: 7,
                                textAlign: 'right',
                                padding: '5px 10px',
                              },
                            }}
                            value={feeDetailsData
                              .filter((item) => item.checked === true)
                              ?.reduce((accum: number, item) => accum + item.termPaid, 0)}
                          />
                        </Stack>
                      </Grid>
                      <Grid item xs={4} sm={2} lg={2} xl={2}>
                        <Stack direction="column" justifyContent="start" alignItems="start">
                          <Typography variant="subtitle1" fontSize={12} color="secondary">
                            Scholarship
                          </Typography>
                          <TextField
                            fullWidth
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                '&:hover fieldset': {
                                  borderColor: isLight ? theme.palette.grey[400] : theme.palette.grey[700],
                                },
                                '&.Mui-focused fieldset': {
                                  borderColor: isLight ? theme.palette.grey[400] : theme.palette.grey[700],
                                },
                              },
                            }}
                            inputProps={{
                              readOnly: true,
                              style: {
                                borderRadius: 7,
                                textAlign: 'right',
                                padding: '5px 10px',
                              },
                            }}
                            FormHelperTextProps={{
                              sx: {
                                margin: 0,
                                fontSize: '9px',
                              },
                            }}
                            value={
                              // checked > 0
                              //   ?
                              feeDetailsData
                                .filter((item) => item.checked === true)
                                ?.reduce((accum: number, item) => accum + item.scholarship, 0)
                              // : feeDetailsData?.reduce((accum: number, item) => accum + item.scholarship, 0)
                            }
                          />
                        </Stack>
                      </Grid>
                      <Grid item xs={4} sm={2} lg={2} xl={2}>
                        <Stack direction="column" justifyContent="start" alignItems="start">
                          <Typography variant="subtitle1" fontSize={12} color="secondary">
                            Discount
                          </Typography>
                          <TextField
                            fullWidth
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                '&:hover fieldset': {
                                  borderColor: isLight ? theme.palette.grey[400] : theme.palette.grey[700],
                                },
                                '&.Mui-focused fieldset': {
                                  borderColor: isLight ? theme.palette.grey[400] : theme.palette.grey[700],
                                },
                              },
                            }}
                            inputProps={{
                              readOnly: true,
                              style: {
                                borderRadius: 7,
                                textAlign: 'right',
                                padding: '5px 10px',
                              },
                            }}
                            FormHelperTextProps={{
                              sx: {
                                margin: 0,
                                fontSize: '9px',
                              },
                            }}
                            value={
                              // checked > 0
                              //   ?
                              feeDetailsData
                                .filter((item) => item.checked === true)
                                ?.reduce((accum: number, item) => accum + item.discount, 0)
                              // : feeDetailsData?.reduce((accum: number, item) => accum + item.discount, 0)
                            }
                          />
                        </Stack>
                      </Grid>
                      <Grid item xs={4} sm={2} lg={2} xl={2}>
                        <Stack direction="column" justifyContent="start" alignItems="start">
                          <Typography variant="subtitle1" fontSize={12} color="secondary">
                            Fine
                          </Typography>
                          <TextField
                            fullWidth
                            sx={{
                              '& .MuiOutlinedInput-root': {
                                '&:hover fieldset': {
                                  borderColor: isLight ? theme.palette.grey[400] : theme.palette.grey[700],
                                },
                                '&.Mui-focused fieldset': {
                                  borderColor: isLight ? theme.palette.grey[400] : theme.palette.grey[700],
                                },
                              },
                            }}
                            inputProps={{
                              readOnly: true,
                              style: {
                                borderRadius: 7,
                                textAlign: 'right',
                                padding: '5px 10px',
                              },
                            }}
                            FormHelperTextProps={{
                              sx: {
                                margin: 0,
                                fontSize: '9px',
                              },
                            }}
                            value={
                              // checked > 0
                              //   ?
                              feeDetailsData
                                .filter((item) => item.checked === true)
                                ?.reduce((accum: number, item) => accum + item.fine, 0)
                              // : feeDetailsData?.reduce((accum: number, item) => accum + item.fine, 0)
                            }
                          />
                        </Stack>
                      </Grid>
                      <Grid item xs={4} sm={2} lg={2} xl={2}>
                        <Stack direction="column" justifyContent="start" alignItems="start">
                          <Stack direction="row" justifyContent="space-between" alignItems="center" gap={0.5}>
                            <Typography variant="subtitle1" fontSize={12}>
                              Payable
                            </Typography>
                            <ModeEditIcon
                              sx={{
                                fontSize: '14px',
                                cursor: 'pointer',
                                color: theme.palette.grey[600],
                              }}
                            />
                          </Stack>
                          <TextField
                            fullWidth
                            inputProps={{
                              // readOnly: true,
                              style: {
                                borderRadius: 7,
                                textAlign: 'right',
                                padding: '5px 10px',
                                fontWeight: 600,
                              },
                            }}
                            FormHelperTextProps={{
                              sx: {
                                margin: 0,
                                fontSize: '9px',
                              },
                            }}
                            error={errorInputTotal}
                            helperText={errorInputTotal && 'Enter a Valid Input'}
                            type="number"
                            value={totalFee === 0 ? '' : totalFee}
                            // onChange={(e) => {
                            //   const prevData = feeDetailsData?.filter((row) =>
                            //     studentsFeeStatusListData?.map(
                            //       (item) => item.feeId === row.feeId && item.termId === row.termId
                            //     )
                            //   );
                            //   console.log(prevData);
                            //   const prevTotal = prevData
                            //     ?.filter((row) => row.checked === true)
                            //     ?.reduce((accum: number, item) => accum + item.fine, 0);
                            //   let total = parseInt(e.target.value, 10);
                            //   setTotalFee(total);
                            //   // const prevTotal = parseInt(e.target.value, 10);
                            //   if (prevTotal > totalFee) {
                            //     setErrorInputTotal(true);
                            //   } else if (prevTotal <= totalFee) {
                            //     setErrorInputTotal(false);
                            //   }
                            //   const newfeeDetailsData = feeDetailsData.map((item) => {
                            //     if (item.checked) {
                            //       if (total < item.termBalance) {
                            //         if (total < 0) {
                            //           return { ...item, checked: false };
                            //         }
                            //         const temp = total;
                            //         total -= item.termBalance;
                            //         return { ...item, termBalance: temp };
                            //       }
                            //       if (
                            //         total <=
                            //         (studentsFeeStatusListData
                            //           ? studentsFeeStatusListData?.filter(
                            //               (row) => item.feeId === row.feeId && item.termId === row.termId
                            //             )[0].termBalance
                            //           : totalFee)
                            //       ) {
                            //         setErrorInputTotal(false);
                            //         return { ...item, termBalance: total };
                            //       }
                            //       total -= item.termBalance;
                            //       return item;
                            //     }
                            //     return item;
                            //   });
                            //   setFeeDetailsData(newfeeDetailsData);
                            //   // FindTotalFee(newfeeDetailsData.filter((item) => item.checked === true));
                            //   // if (total > 0) {
                            //   //   setTotalFee(total.toString());
                            //   // }
                            //   // }
                            // }}
                            onChange={(e) => {
                              const prevTotal = studentsFeeStatusListData
                                ? studentsFeeStatusListData?.reduce(
                                    (accum: number, item) => accum + item.termBalance,
                                    0
                                  )
                                : 0;
                              let total = parseInt(e.target.value, 10);
                              setTotalFee(total);
                              // const prevTotal = parseInt(e.target.value, 10);
                              if (prevTotal < totalFee) {
                                setErrorInputTotal(true);
                              } else if (prevTotal >= totalFee) {
                                setErrorInputTotal(false);
                              }
                              const newfeeDetailsData = studentsFeeStatusListData
                                ? studentsFeeStatusListData.map((item) => {
                                    if (total > 0 && item.termBalance !== 0) {
                                      if (
                                        total <=
                                        (studentsFeeStatusListData
                                          ? studentsFeeStatusListData?.filter(
                                              (row) => item.feeId === row.feeId && item.termId === row.termId
                                            )[0].termBalance
                                          : totalFee)
                                      ) {
                                        const temp = total;
                                        total -= item.termBalance;
                                        return { ...item, termBalance: temp, checked: true };
                                      }
                                      total -= item.termBalance;
                                      return { ...item, checked: true };
                                    }
                                    return { ...item, checked: false };
                                  })
                                : [];
                              setFeeDetailsData(newfeeDetailsData);
                            }}
                          />
                        </Stack>
                      </Grid>
                    </Grid>
                  </Box>
                </Paper>
              </Grid>
              <Grid item xs={12} md={12} lg={isCollapsed ? 2 : 4} xl={isCollapsed ? 2 : 4}>
                {/* <Box display="flex" justifyContent="flex-end" p={1}>
                  <Button variant="outlined" size="small" onClick={handleToggleView}>
                    Full View
                  </Button>
                </Box> */}
                <Card sx={{ border: 1, borderColor: theme.palette.grey[300], boxShadow: 0, borderRadius: 1 }}>
                  <Box display="flex" flexDirection="column" textAlign="start" sx={{ p: 2 }}>
                    <Stack direction="row" justifyContent="start" gap={2} alignItems="center" pb={2}>
                      <Avatar
                        sx={{ width: 100, height: 100 }}
                        // alt={selectedStudentData[0]?.studentName}
                        alt={studentSelected?.studentName}
                        // src={selectedStudentData[0]?.image}
                        // src={
                        //   selectedStudentData[0]?.image !== 'http://demo.passdaily.in/Photos/'
                        //     ? selectedStudentData[0]?.image
                        //     : selectedStudentData[0]?.gender === 'Male'
                        //     ? man
                        //     : selectedStudentData[0]?.gender === 'Female'
                        //     ? woman
                        //     : ''
                        // }
                        src={
                          studentSelected?.image !== 'http://demo.passdaily.in/Photos/'
                            ? studentSelected?.image
                            : studentSelected?.gender === 'Male'
                            ? man
                            : studentSelected?.gender === 'Female'
                            ? woman
                            : ''
                        }
                      />
                      <Typography variant="subtitle2" fontSize={16}>
                        {/* {selectedStudentData[0]?.studentName || '--'} */}
                        {studentSelected?.studentName || '--'}
                      </Typography>
                    </Stack>
                    <Stack position="absolute" top={10} right={10}>
                      <Chip
                        size="small"
                        // label={`AD : ${selectedStudentData[0]?.admissionNo || '--'}`}
                        label={`AD : ${studentSelected?.admissionNo || '--'}`}
                        variant="outlined"
                        color="info"
                        sx={{
                          border: '0px',
                          fontWeight: 600,
                          width: 'fit-content',
                          // backgroundColor: theme.palette.primary.lighter,
                          // backgroundColor: '#8992fb ',
                          // color: theme.palette.primary.main,
                          // color: 'white',
                        }}
                      />
                    </Stack>
                    <Grid container rowSpacing={1}>
                      {/* <Grid item xxl={2} lg={4} md={2} sm={2} xs={4}>
                        <Typography whiteSpace="nowrap" variant="subtitle2" color="GrayText" fontSize={13}>
                          AD
                        </Typography>
                      </Grid> */}
                      {/* <Grid item xxl={10} lg={8} md={10} sm={10} xs={8} mt="auto">
                        <Typography variant="subtitle2" fontSize={13}>
                          : {selectedStudentData[0]?.admissionNo || '--'}
                        </Typography>
                      </Grid> */}
                      <Grid item xxl={2} lg={4} md={2} sm={2} xs={4} mt="auto">
                        <Typography variant="subtitle2" color="GrayText" fontSize={13}>
                          Class
                        </Typography>
                      </Grid>
                      <Grid item xxl={10} lg={8} md={10} sm={10} xs={8}>
                        <Typography variant="subtitle2" fontSize={13}>
                          {/* : {selectedStudentData[0]?.className || '--'} */}:&nbsp;
                          {studentSelected?.className || '--'}
                        </Typography>
                      </Grid>
                      <Grid item xxl={2} lg={4} md={2} sm={2} xs={4}>
                        <Typography variant="subtitle2" color="GrayText" fontSize={13}>
                          Gender
                        </Typography>
                      </Grid>
                      <Grid item xxl={10} lg={8} md={10} sm={10} xs={8} mt="auto">
                        <Typography variant="subtitle2" fontSize={13}>
                          {/* : {selectedStudentData[0]?.gender || '--'} */}:&nbsp;{studentSelected?.gender || '--'}
                        </Typography>
                      </Grid>
                      <Grid item xxl={2} lg={4} md={2} sm={2} xs={4}>
                        <Typography variant="subtitle2" color="GrayText" fontSize={13}>
                          Parent
                        </Typography>
                      </Grid>
                      <Grid item xxl={10} lg={8} md={10} sm={10} xs={8} mt="auto">
                        <Typography variant="subtitle2" fontSize={13}>
                          :&nbsp;
                          <EllipsisWithTooltip
                            // text={selectedStudentData[0]?.fatherName || '--'}
                            text={studentSelected?.guardianName || '--'}
                          />
                        </Typography>
                      </Grid>
                      <Grid item xxl={2} lg={4} md={2} sm={2} xs={4}>
                        <Typography variant="subtitle2" color="GrayText" fontSize={13}>
                          Number
                        </Typography>
                      </Grid>
                      <Grid item xxl={6} lg={8} md={10} sm={10} xs={8} mt="auto">
                        <Typography variant="subtitle2" fontSize={13}>
                          {/* : {selectedStudentData[0]?.fatherNumber || '--'} */}:&nbsp;
                          {studentSelected?.guardianNumber || '--'}
                        </Typography>
                      </Grid>
                    </Grid>
                  </Box>
                  <Box p={1} mb={2} mt={1} sx={{ backgroundColor: theme.palette.primary.lighter }}>
                    <Typography variant="subtitle2" align="center" color={theme.palette.primary.dark} fontSize={15}>
                      Total Payable&nbsp;:&nbsp;
                      {Number(
                        checked > 0
                          ? feeDetailsData
                              .filter((item) => item.checked === true)
                              ?.reduce((accum: number, item) => accum + item.termBalance, 0)
                          : studentsFeeStatusListData?.reduce((accum: number, item) => accum + item.termBalance, 0)
                      ).toLocaleString('en-IN')}
                    </Typography>
                  </Box>
                  <Box pb={0}>
                    <Typography variant="h6" fontSize={14} pl={2}>
                      Late Fee Information:
                    </Typography>
                    <Table className="lateFee_info" sx={{ height: 145 }} size="small" aria-label="a dense table">
                      <TableHead>
                        <TableRow
                          sx={{ backgroundColor: isLight ? theme.palette.info.lighter : theme.palette.grey[900] }}
                        >
                          <TableCell>Name</TableCell>
                          <TableCell>Days</TableCell>
                          <TableCell>Amt</TableCell>
                          <TableCell>Paid</TableCell>
                          <TableCell>Remark</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        <TableRow>
                          <TableCell> </TableCell>
                          <TableCell> </TableCell>
                          <TableCell> </TableCell>
                          <TableCell> </TableCell>
                          <TableCell> </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </Box>
                </Card>
              </Grid>
            </Grid>
            <Box>
              <Stack mt={5} mb={1} direction="row" justifyContent="space-between" alignItems="center" width="100%">
                <Typography fontSize={14} variant="subtitle2">
                  Payment Information:
                </Typography>
                <Button
                  size="small"
                  type="button"
                  color="secondary"
                  variant="contained"
                  startIcon={<AddIcon />}
                  onClick={handleAddRow}
                  sx={{ py: 0.5, px: 1 }}
                >
                  Add Row
                </Button>
              </Stack>

              <form noValidate>
                <TableContainer sx={{ border: 1, borderColor: theme.palette.grey[300], borderRadius: 1 }}>
                  <Table sx={{ minWidth: 1100 }} aria-label="simple table" stickyHeader>
                    <TableHead>
                      <TableRow className="Payment_Info">
                        <TableCell sx={{ whiteSpace: 'nowrap' }}>Sl.No</TableCell>
                        <TableCell sx={{ whiteSpace: 'nowrap' }}>Payment Type</TableCell>
                        <TableCell sx={{ whiteSpace: 'nowrap' }}>Amount</TableCell>
                        <TableCell sx={{ whiteSpace: 'nowrap' }}>Cheque/DD No</TableCell>
                        <TableCell sx={{ whiteSpace: 'nowrap' }}>Date of Issue</TableCell>
                        <TableCell sx={{ whiteSpace: 'nowrap' }}>Deposit Date</TableCell>
                        <TableCell sx={{ whiteSpace: 'nowrap' }}>Clearing Date</TableCell>
                        <TableCell colSpan={2}>Bank</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {rows.map((row) => (
                        <TableRow key={row.id}>
                          <TableCell>{row.id}</TableCell>
                          <TableCell width="12%">
                            <Select
                              sx={{
                                // backgroundColor: isLight ? theme.palette.grey[200] : theme.palette.grey[900],
                                height: 30,
                                fontSize: 12,
                              }}
                              name=""
                              fullWidth
                              labelId="paymentTypeFilter"
                              id="paymentTypeFilterSelect"
                              value={rows[row.id - 1].paymentTypeId?.toString()}
                              onChange={(e) =>
                                setRows((prevItems) => {
                                  const newRows = [...prevItems];
                                  newRows[row.id - 1].paymentTypeId = parseInt(e.target.value, 10);
                                  return newRows;
                                })
                              }
                              placeholder="Select Type"
                            >
                              <MenuItem value={0} sx={{ display: 'none' }}>
                                Select Type
                              </MenuItem>
                              {paymentTypesData &&
                                paymentTypesData.map((opt) => (
                                  <MenuItem key={opt.paymentTypeId} value={opt.paymentTypeId}>
                                    {opt.paymentType}
                                  </MenuItem>
                                ))}
                            </Select>
                          </TableCell>
                          <TableCell width={130}>
                            <TextField
                              fullWidth
                              value={rows[row.id - 1].amount}
                              placeholder="Enter amount"
                              onChange={(e) =>
                                setRows((prevItems) => {
                                  const newRows = [...prevItems];
                                  newRows[row.id - 1].amount = e.target.value;
                                  return newRows;
                                })
                              }
                              inputProps={{
                                style: {
                                  padding: '6px 10px',
                                  fontSize: 12,
                                  borderRadius: 7,
                                  // backgroundColor: isLight ? theme.palette.grey[200] : theme.palette.grey[900],
                                },
                              }}
                              name=""
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell width={180}>
                            <TextField
                              value={rows[row.id - 1].chequeOrDDNo?.toString()}
                              placeholder="Enter number"
                              onChange={(e) =>
                                setRows((prevItems) => {
                                  const newRows = [...prevItems];
                                  newRows[row.id - 1].chequeOrDDNo = e.target.value;
                                  return newRows;
                                })
                              }
                              inputProps={{
                                style: {
                                  padding: '6px 10px',
                                  fontSize: 12,
                                  borderRadius: 7,
                                  // backgroundColor: isLight ? theme.palette.grey[200] : theme.palette.grey[900],
                                },
                              }}
                              name=""
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell width={180}>
                            <DatePickers
                              fontSize={12}
                              inputFeildPadding="0.4rem 0.6rem"
                              name="dateOfIssue"
                              value={dayjs(rows[row.id - 1].dateOfIssue, 'DD/MM/YYYY')}
                              onChange={(event) => handleDateChange(event, row, 'dateOfIssue')}
                            />
                          </TableCell>
                          <TableCell width={180}>
                            <DatePickers
                              fontSize={12}
                              inputFeildPadding="0.4rem 0.6rem"
                              name="depositDate"
                              value={dayjs(rows[row.id - 1].deppositDate, 'DD/MM/YYYY')}
                              onChange={(event) => handleDateChange(event, row, 'deppositDate')}
                            />
                          </TableCell>
                          <TableCell width={180}>
                            <DatePickers
                              fontSize={12}
                              inputFeildPadding="0.4rem 0.6rem"
                              name="clearingDate"
                              value={dayjs(rows[row.id - 1].clearingDate, 'DD/MM/YYYY')}
                              onChange={(event) => handleDateChange(event, row, 'clearingDate')}
                            />
                          </TableCell>
                          <TableCell>
                            <Select
                              MenuProps={{
                                PaperProps: {
                                  style: {
                                    maxHeight: '300px', // Adjust the value to your desired height
                                  },
                                },
                              }}
                              sx={{
                                fontSize: 12,
                                height: 30,
                                // backgroundColor: isLight ? theme.palette.grey[200] : theme.palette.grey[900],
                              }}
                              name=""
                              fullWidth
                              labelId="bankDetailsFilter"
                              id="bankDetailsFilterSelect"
                              value={rows[row.id - 1].bankId?.toString()}
                              onChange={(e) =>
                                setRows((prevItems) => {
                                  const newRows = [...prevItems];
                                  newRows[row.id - 1].bankId = parseInt(e.target.value, 10);
                                  return newRows;
                                })
                              }
                              placeholder="Select Bank"
                            >
                              <MenuItem value={0} sx={{ display: 'none' }}>
                                Select Bank
                              </MenuItem>
                              {bankDetailsData &&
                                bankDetailsData.map((opt) => (
                                  <MenuItem key={opt.bankId} value={opt.bankId}>
                                    {opt.bankName}
                                  </MenuItem>
                                ))}
                            </Select>
                          </TableCell>
                          <TableCell>
                            <IconButton
                              size="small"
                              color="error"
                              aria-label="Delete Row"
                              onClick={() => handleDeleteRow(row.id)}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </form>
            </Box>
            <Box
              // flexWrap={{ xs: 'wrap', md: 'nowrap' }}
              rowGap={{ xs: 4, sm: 0 }}
              mt={3}
            >
              <Grid container columnSpacing={2} rowSpacing={1} py={1.5}>
                <Grid item xs={5} sm={3} md={3.5} lg={2.5} xl={2.5}>
                  <Stack direction="column" justifyContent="start" alignItems="start" width="100%">
                    <Typography variant="subtitle1" fontSize={12} color="secondary">
                      Paying Date
                    </Typography>
                    <DatePickers
                      maxDate={currentDate}
                      name="Paying Date"
                      value={dayjs(payingDate, 'DD/MM/YYYY')}
                      onChange={(event) => handlePayingDateChange(event)}
                    />
                  </Stack>
                </Grid>
                <Grid item xs={7} sm={3} md={3.5} lg={2.5} xl={2}>
                  <Stack direction="column" justifyContent="start" alignItems="start" width="100%">
                    <Typography variant="subtitle1" fontSize={12} color="secondary">
                      Paying Receipt No
                    </Typography>
                    <TextField
                      fullWidth
                      id="receiptNumber"
                      type="number"
                      placeholder="Enter number"
                      value={payingReceiptNo}
                      onChange={(e) => setPayingReceiptNo(e.target.value)}
                    />
                  </Stack>
                </Grid>
                <Grid item xs={12} sm={6} md={5} lg={6} xl={4}>
                  <Stack direction="column" justifyContent="start" alignItems="start" width="100%">
                    <Typography variant="subtitle1" fontSize={12} color="secondary">
                      Remarks
                    </Typography>
                    <TextField
                      fullWidth
                      value={remarks}
                      onChange={(e) => setRemarks(e.target.value)}
                      multiline
                      placeholder="Type here..."
                      inputProps={{
                        style: {
                          resize: 'vertical',
                          minHeight: '20px',
                          maxHeight: '100px',
                          // backgroundColor: isLight ? theme.palette.grey[200] : theme.palette.grey[900],
                          borderRadius: 7,
                        },
                      }}
                      id=""
                      label=""
                    />
                  </Stack>
                </Grid>
              </Grid>
              <Box pt={2} display="flex" width="100%" sx={{ justifyContent: { xs: 'center', sm: 'right' } }}>
                <Stack spacing={2} direction="row" justifyContent="end" width="100%">
                  <Button
                    sx={{ width: { xs: '100%', sm: 100 } }}
                    variant="contained"
                    color="secondary"
                    size="medium"
                    onClick={() => {
                      setFeeDetailsData((preData) => {
                        return preData.map((item) => {
                          return { ...item, checked: false };
                        });
                      });
                      setTotalFee(0);
                      setRemarks('');
                      setPayingDate('');
                      setPayingReceiptNo('');
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    sx={{ width: { xs: '100%', sm: 100 } }}
                    variant="contained"
                    size="medium"
                    disabled={checked === 0 || errorInputTotal}
                    onClick={handleSave}
                  >
                    Pay
                  </Button>
                </Stack>
              </Box>
            </Box>
          </div>
        </Card>
      </FeesDetailsRoot>
      <TemporaryDrawer
        closeIconDisable
        onClose={() => {
          handleClose();
          loadFeeDetailsList(currentFeeDetailsRequest);
        }}
        state={drawerOpen}
        DrawerContent={
          <Pay
            selectedTerms={feeDetailsData.filter((item) => item.checked === true)}
            sendReq={sendReq}
            onClose={() => setDrawerOpen(false)}
            onCancel={handleClose}
            load={() => loadFeeDetailsList(currentFeeDetailsRequest)}
            studentDetails={studentSelected}
            receiptType={receiptType}
            paymentTypesData={paymentTypesData}
          />
        }
      />
      <Snackbar
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={() => setSnackbarOpen(false)}
      >
        <Alert onClose={() => setSnackbarOpen(false)} severity="error" variant="filled" sx={{ width: '100%' }}>
          Receipt number already exist
        </Alert>
      </Snackbar>
    </Page>
  );
}
