import { Box, Grid, MenuItem, Skeleton, SelectChangeEvent, Stack, Typography, Card, Select } from '@mui/material';
import styled, { useTheme } from 'styled-components';
import CurrencyRupeeIcon from '@mui/icons-material/CurrencyRupee';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import { getClassData, getFeeOverviewStatusListData, getFeeOverviewStatusListStatus } from '@/config/storeSelectors';
import { ClassListInfo } from '@/types/AcademicManagement';
import React, { useEffect, useState } from 'react';
import { fetchFeeOverviewStatus } from '@/store/ManageFee/manageFee.thunks';
import useAuth from '@/hooks/useAuth';
import { fetchClassList } from '@/store/Dashboard/dashboard.thunks';
import useSettings from '@/hooks/useSettings';
import { OverViewProps } from '@/types/Common';

const FeeRoot = styled.div`
  /* background-color: ${(props) =>
    props.theme.themeMode === 'light' ? props.theme.palette.success.lighter : props.theme.palette.grey[900]}; */
`;

export const Fee = ({ academicId, feeTypeId }: OverViewProps) => {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const { user } = useAuth();
  const adminId: number | undefined = user?.accountId;
  const dispatch = useAppDispatch();
  const feeOverviewStatusListData = useAppSelector(getFeeOverviewStatusListData);
  const feeOverviewStatusListStatus = useAppSelector(getFeeOverviewStatusListStatus);

  const ClassData = useAppSelector(getClassData);

  const AllClassOption: ClassListInfo = {
    classId: -1,
    className: 'All Class',
    classDescription: 'string',
    classStatus: 1,
  };

  const classDataWithAllClass = [AllClassOption, ...ClassData];
  const [classOptions, setClassOptions] = useState<ClassListInfo>(classDataWithAllClass[0]);

  const { classId, className } = classOptions || {};
  const {
    prevoiusMonthCollection,
    thisMonthCollection,
    thisYearTotalBalance,
    thisYearTotalFee,
    thisYearTotalPaid,
    todaysCollection,
  } = feeOverviewStatusListData || {};

  const handleChange = (event: SelectChangeEvent) => {
    const selectedClass = classDataWithAllClass.find((item) => item.className === event.target.value);
    if (selectedClass) {
      setClassOptions(selectedClass);
    }
  };

  useEffect(() => {
    dispatch(fetchClassList(adminId));
    dispatch(fetchFeeOverviewStatus({ adminId, academicId, classId, feeTypeId }));
    console.log('classId::::', classId);
  }, [dispatch, adminId, classId, academicId, feeTypeId]);
  const status = feeOverviewStatusListStatus === 'loading';
  return (
    <FeeRoot>
      <Card
        sx={{ height: { xs: '100%', lg: '235px' }, backgroundColor: isLight ? '#d2f3dd' : theme.palette.grey[900] }}
      >
        <Box display="flex" p={2} alignItems="center" justifyContent="space-between">
          <Typography variant="h6" fontSize={14}>
            Fee Status
          </Typography>
          <Stack direction={{ xs: 'column', sm: 'row' }}>
            {/* <SelectBox Selection_Options={YEAR_SELECT} placeholder="Year" /> */}
            <Select
              sx={{ backgroundColor: theme.palette.common.white, color: theme.palette.primary.main, height: 30 }}
              value={className}
              onChange={handleChange}
              displayEmpty
              labelId="demo-dialog-select-label"
              id="demo-dialog-select"
              inputProps={{ 'aria-label': 'Without label' }}
              MenuProps={{
                PaperProps: {
                  style: {
                    maxHeight: '250px', // Adjust the value to your desired height
                  },
                },
              }}
            >
              {/* <MenuItem value={className} className="d-none">
              {className}
            </MenuItem> */}
              {classDataWithAllClass?.map((item: ClassListInfo) => (
                <MenuItem sx={{ fontSize: '13px' }} key={item.classId} value={item.className}>
                  {item.className}
                </MenuItem>
              ))}
            </Select>
          </Stack>
        </Box>
        <Grid
          container
          spacing={2}
          sx={{
            px: { xs: 3, md: 3 },
            py: { xs: 2, md: 3 },
            // height: { xs: '', md: 226 },
            flexWrap: 'wrap',
          }}
        >
          <Grid item lg={4} md={4} sm={4} xs={12} sx={{ flexBasis: '100%', maxWidth: '100%' }}>
            <Typography variant="subtitle2" pb={2} fontWeight={600} color={theme.palette.secondary.main}>
              This Year Total Fee
            </Typography>

            <Stack direction="row" alignItems="center">
              <CurrencyRupeeIcon sx={{ color: 'green' }} />
              {status ? (
                <Skeleton variant="rounded" sx={{ backgroundColor: theme.palette.grey[100] }} width={170} height={20} />
              ) : (
                <>
                  <Typography fontWeight={600}>{Number(thisYearTotalFee).toLocaleString('en-IN')}</Typography>
                  <Box fontWeight={600}>
                    {thisYearTotalFee === 60000000 ? (
                      <Box
                        bgcolor="#bbf7d0"
                        px={1.5}
                        ml={1}
                        color="green"
                        sx={{ display: 'inline', borderRadius: '20px' }}
                      >
                        <TrendingUpIcon />
                      </Box>
                    ) : (
                      <Box
                        bgcolor="#fecaca"
                        px={1.5}
                        ml={1}
                        color="red"
                        sx={{ display: 'inline', borderRadius: '20px' }}
                      >
                        <TrendingUpIcon />
                      </Box>
                    )}
                  </Box>
                </>
              )}
            </Stack>
          </Grid>
          <Grid item lg={4} md={4} sm={4} xs={12} sx={{ flexBasis: '100%', maxWidth: '100%' }}>
            <Typography variant="subtitle2" pb={2} fontWeight={600} color={theme.palette.secondary.main}>
              This Year Total Paid
            </Typography>
            <Stack direction="row" alignItems="center">
              <CurrencyRupeeIcon sx={{ color: 'green' }} />
              {status ? (
                <Skeleton variant="rounded" sx={{ backgroundColor: theme.palette.grey[100] }} width={170} height={20} />
              ) : (
                <>
                  <Typography fontWeight={600}>{Number(thisYearTotalPaid).toLocaleString('en-IN')}</Typography>
                  <Box fontWeight={600}>
                    {thisYearTotalPaid === 60000000 ? (
                      <Box
                        bgcolor="#bbf7d0"
                        px={1.5}
                        ml={1}
                        color="green"
                        sx={{ display: 'inline', borderRadius: '20px' }}
                      >
                        <TrendingUpIcon />
                      </Box>
                    ) : (
                      <Box
                        bgcolor="#fecaca"
                        px={1.5}
                        ml={1}
                        color="red"
                        sx={{ display: 'inline', borderRadius: '20px' }}
                      >
                        <TrendingUpIcon />
                      </Box>
                    )}
                  </Box>
                </>
              )}
            </Stack>
          </Grid>
          <Grid item lg={4} md={4} sm={4} xs={12} sx={{ flexBasis: '100%', maxWidth: '100%' }}>
            <Typography variant="subtitle2" pb={2} fontWeight={600} color={theme.palette.secondary.main}>
              This Year Total Balance
            </Typography>
            <Stack direction="row">
              <CurrencyRupeeIcon sx={{ color: 'green' }} />
              {status ? (
                <Skeleton variant="rounded" sx={{ backgroundColor: theme.palette.grey[100] }} width={170} height={20} />
              ) : (
                <>
                  <Typography fontWeight={600}>{Number(thisYearTotalBalance).toLocaleString('en-IN')}</Typography>
                  <Box fontWeight={600}>
                    {thisYearTotalPaid === 60000000 ? (
                      <Box
                        bgcolor="#bbf7d0"
                        px={1.5}
                        ml={1}
                        color="green"
                        sx={{ display: 'inline', borderRadius: '20px' }}
                      >
                        <TrendingUpIcon />
                      </Box>
                    ) : (
                      <Box
                        bgcolor="#fecaca"
                        px={1.5}
                        ml={1}
                        color="red"
                        sx={{ display: 'inline', borderRadius: '20px' }}
                      >
                        <TrendingUpIcon />
                      </Box>
                    )}
                  </Box>
                </>
              )}
            </Stack>
          </Grid>
          <Grid item lg={4} md={4} sm={4} xs={12} sx={{ flexBasis: '100%', maxWidth: '100%', mt: { xs: 2, md: 3 } }}>
            <Typography variant="subtitle2" pb={2} fontWeight={600} color={theme.palette.secondary.main}>
              Previous Month Collection
            </Typography>
            <Stack direction="row" alignItems="center">
              <CurrencyRupeeIcon sx={{ color: 'green' }} />
              {status ? (
                <Skeleton variant="rounded" sx={{ backgroundColor: theme.palette.grey[100] }} width={170} height={20} />
              ) : (
                <Typography fontWeight={600}>{Number(prevoiusMonthCollection).toLocaleString('en-IN')}</Typography>
              )}
            </Stack>
          </Grid>

          <Grid item lg={4} md={4} sm={4} xs={12} sx={{ flexBasis: '100%', maxWidth: '100%', mt: { xs: 2, md: 3 } }}>
            <Typography variant="subtitle2" pb={2} fontWeight={600} color={theme.palette.secondary.main}>
              This Month Collection
            </Typography>
            <Stack direction="row" alignItems="center">
              <CurrencyRupeeIcon sx={{ color: 'green' }} />
              {status ? (
                <Skeleton variant="rounded" sx={{ backgroundColor: theme.palette.grey[100] }} width={170} height={20} />
              ) : (
                <Typography fontWeight={600}>{Number(thisMonthCollection).toLocaleString('en-IN')}</Typography>
              )}
            </Stack>
          </Grid>
          <Grid item lg={4} md={4} sm={4} xs={12} sx={{ flexBasis: '100%', maxWidth: '100%', mt: { xs: 2, md: 3 } }}>
            <Typography variant="subtitle2" pb={2} fontWeight={600} color={theme.palette.secondary.main}>
              Todays Collection
            </Typography>
            <Stack direction="row" alignItems="center">
              <CurrencyRupeeIcon sx={{ color: 'green' }} />
              {status ? (
                <Skeleton variant="rounded" sx={{ backgroundColor: theme.palette.grey[100] }} width={170} height={20} />
              ) : (
                <Typography fontWeight={600}>{Number(todaysCollection).toLocaleString('en-IN')}</Typography>
              )}
            </Stack>
          </Grid>
        </Grid>
      </Card>
    </FeeRoot>
  );
};
