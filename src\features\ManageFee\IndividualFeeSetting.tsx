/* eslint-disable no-prototype-builtins */
/* eslint-disable no-else-return */
/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { ChangeEvent, FormEvent, useCallback, useMemo, useState, useRef, useEffect } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  Button,
  Typography,
  Card,
  Avatar,
  IconButton,
  Collapse,
  Select,
  MenuItem,
  FormControl,
  useTheme,
  SelectChangeEvent,
  Tooltip,
  Radio,
  Skeleton,
  TextField,
  InputAdornment,
  CircularProgress,
} from '@mui/material';
import NoData from '@/assets/no-datas.png';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import SaveIcon from '@mui/icons-material/Save';
import CloseIcon from '@mui/icons-material/Close';
import Success from '@/assets/ManageFee/Success.json';
import Updated from '@/assets/ManageFee/Updated.json';
import UpdateLoading from '@/assets/ManageFee/UpdateLoading.json';
import SaveLoading from '@/assets/ManageFee/SaveLoading.json';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import errorIcon from '@/assets/ManageFee/Error.json';
import deleteSuccess from '@/assets/ManageFee/deleteSuccess.json';
import studentSuccessIcon from '@/assets/ManageFee/studentSuccessJsonIcon.json';
import studentSelectedIcon from '@/assets/ManageFee/studentSelectedIcon.json';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import styled from 'styled-components';
import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import useSettings from '@/hooks/useSettings';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getClassSectionsData,
  getDashboardYearSubmitting,
  getIndividualFeeSettingListData,
  getIndividualFeeSettingListStatus,
  getManageFeeclassListData,
  getYearData,
  getYearStatus,
} from '@/config/storeSelectors';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import {
  DeleteAllOptionalFee,
  DeleteOptionalFee,
  // DeleteAllOptionalFee,
  // DeleteOptionalFee,
  fetchClassList,
  fetchClassSections,
  fetchGetOptionalFeeSettingsIndividual,
  optionalFeeSettingsIndividual,
} from '@/store/ManageFee/manageFee.thunks';
import {
  BasicFeeMappedDeleteAllDataType,
  FeeMappedType,
  GetIndividualFeeSettingsDataType,
  GetIndividualFeeStudentMapDataType,
  IndividualFeeMappedDataType,
  OptionalFeeMappedDeleteAllType,
} from '@/types/ManageFee';
import { SuccessIcon } from '@/theme/overrides/CustomIcons';
import Lottie from 'lottie-react';
import LoadingButton from '@mui/lab/LoadingButton';
import PositionedSnackbar from '@/components/shared/Popup/SnackBar';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { FEE_TYPE_ID_OPTIONS } from '@/config/Selection';
import DTVirtuoso from '@/components/shared/RND/DataTableVirtuoso';
import CurrencyRupeeIcon from '@mui/icons-material/CurrencyRupee';
import AddIcon from '@mui/icons-material/Add';
import StudentsPickerField from '@/components/shared/Selections/StudentsPicker';
import { Checkbox } from '@mui/material';

const IndividualFeeSettingRoot = styled.div`
  padding: 1rem;
  @media screen and (max-width: 576px) {
    padding: 0.5rem;
  }
  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 576px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        /* border: 1px solid ${(props) => props.theme.palette.grey[200]}; */
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }

        .MuiTableCell-root {
          border: 1px solid ${(props) => props.theme.palette.grey[100]};
        }
        .MuiTableCell-root.MuiTableCell-body {
          border: 1px solid ${(props) => props.theme.palette.grey[100]};
        }
        .MuiTableCell-head {
          background-color: ${(props) =>
            props.theme.themeMode === 'light' ? '#CFE1B9' : props.theme.palette.grey[900]};
        }
        @media screen and (min-width: 1200px) {
          .MuiTableCell-head:nth-child(1) {
            z-index: 11;
            position: sticky;
            left: 0;
            width: 50px;
          }
          .MuiTableCell-head:nth-child(2) {
            z-index: 11;
            position: sticky;
            left: 52.5px;
            width: 100px;
          }
          .MuiTableCell-head:nth-child(3) {
            z-index: 11;
            position: sticky;
            left: 164px;
            width: 200px;
          }
          .MuiTableCell-head:last-child {
            z-index: 11;
            position: sticky;
            right: 0px;
            width: 80px;
          }
          .MuiTableCell-root.MuiTableCell-body:nth-child(1) {
            position: sticky;
            left: 0;
            background-color: ${(props) =>
              props.theme.themeMode === 'light' ? '#E9F5DB' : props.theme.palette.grey[900]};
            width: 50px;
            z-index: 1;
          }
          .MuiTableCell-root.MuiTableCell-body:nth-child(2) {
            position: sticky;
            left: 52.5px;
            padding-left: 5px;
            background-color: ${(props) =>
              props.theme.themeMode === 'light' ? '#E9F5DB' : props.theme.palette.grey[900]};
            width: 100px;
            z-index: 1;
          }
          .MuiTableCell-root.MuiTableCell-body:nth-child(3) {
            position: sticky;
            left: 164px;
            background-color: ${(props) =>
              props.theme.themeMode === 'light' ? '#E9F5DB' : props.theme.palette.grey[900]};
            width: 200px;
            z-index: 1;
          }
          .MuiTableCell-root.MuiTableCell-body:last-child {
            position: sticky;
            right: 0px;
            background-color: ${(props) =>
              props.theme.themeMode === 'light' ? '#E9F5DB' : props.theme.palette.grey[800]};
            width: 80px;
            z-index: 1;
          }
        }
        .cellActive {
          border-color: ${(props) => props.theme.palette.warning.main};
          background: linear-gradient(90deg, rgba(255, 249, 232, 1) 0%, rgba(255, 255, 255, 1) 100%);
        }
        .cellInActive {
          border-color: ${(props) => props.theme.palette.success.main};
          background: linear-gradient(90deg, #e3f5d3 0%, rgba(255, 255, 255, 1) 100%);
        }
        .inputCellActive {
          color: ${(props) => props.theme.palette.grey[400]};
          background-color: ${(props) => props.theme.palette.grey[50]};
        }
        .inputCellInActive {
          color: ${(props) => props.theme.palette.grey[500]};
        }
        .MuiTableCell-root.MuiTableCell-body {
          padding: 0px;
          height: 100%;
        }

        .MuiTableCell-root:first-child {
          padding-left: 10px;
        }
      }
    }
  }
`;

interface ExtendGetIndividualFeeSettingsDataType extends GetIndividualFeeStudentMapDataType {
  studentId?: number;
}

export default function IndividualFeeSetting() {
  const theme = useTheme();
  const isLight = useSettings().themeMode === 'light';
  const { user } = useAuth();
  const { confirm } = useConfirm();
  const dispatch = useAppDispatch();
  const adminId: number = user ? user.accountId : 0;
  const academicId = 10;
  const [showFilter, setShowFilter] = useState(true);

  const YearData = useAppSelector(getYearData);
  const YearSubmitting = useAppSelector(getDashboardYearSubmitting);
  const YearStatus = useAppSelector(getYearStatus);
  const [classSectionsFilter, setClassSectionsFilter] = useState(0);
  const [classFilter, setClassFilter] = useState(0);
  const ClassSectionsData = useAppSelector(getClassSectionsData);
  const classListData = useAppSelector(getManageFeeclassListData);
  // const classListStatus = useAppSelector(getclassListStatus);

  const defaultYear = YearData[0]?.accademicId || 0;
  const [academicYearFilter, setAcademicYearFilter] = useState(defaultYear);
  const [feeTypeFilter, setFeeTypeFilter] = useState(0);

  const individualFeeSettingListData = useAppSelector(getIndividualFeeSettingListData);
  const individualFeeSettingListStatus = useAppSelector(getIndividualFeeSettingListStatus);

  const [optionalFeeData, setOptionalFeeData] = useState<any>([]);
  const [studentsData, setStudentsData] = useState<GetIndividualFeeStudentMapDataType[]>([]);
  const [clickedCells, setClickedCells] = useState<{ studentId: number; feeId: number; value?: string }[]>([]);
  const [selectedCells, setSelectedCells] = useState<{ studentId: number; feeId: number }[]>([]);
  const [saving, setSaving] = useState(false);
  const [savingAll, setSavingAll] = useState(false);
  const [individualSaveLoading, setIndividualSaveLoading] = useState<Record<string, boolean>>({});
  const [succesResponse, setSuccesResponse] = useState('');
  const [showSuccessIcon, setShowSuccessIcon] = useState<{ [key: string]: boolean }>({});
  const [individualSaveButtonEnabled, setIndividualSaveButtonEnabled] = useState<{ [key: string]: boolean }>({});
  const [showErrorIcon, setShowErrorIcon] = useState<{ [key: string]: boolean }>({});
  const [checkedRows, setCheckedRows] = useState<{ [key: string]: boolean }>({});
  const [rowId, setRowId] = useState<{ [key: string]: number }>({});
  const [saveAllButtonDisabled, setSaveAllButtonDisabled] = useState<boolean>(true);
  const [snackBar, setSnackBar] = useState<boolean>(false);
  const [selectedRows, setSelectedRows] = useState<ExtendGetIndividualFeeSettingsDataType[]>([]);
  const [selectedStudentIds, setSelectedStudentIds] = useState<string>('');
  const [feeAmounts, setFeeAmounts] = useState<{ [key: string]: number }>({});
  const [enteredCells, setEnteredCells] = useState<{ [key: string]: number }>({});
  const [enteredValues, setEnteredValues] = useState<number[]>([]);
  const [amountEnter, setAmountEnter] = useState<number | string>('');
  const [cellErrorMessages, setCellErrorMessages] = useState<{ [key: string]: string }>({});
  const [isAutoFocus, setIsAutoFocus] = useState<{ [key: string]: boolean }>({});
  const [focusedCellKey, setFocusedCellKey] = useState<string | null>(null);
  const inputRef = useRef<{ [key: string]: HTMLInputElement | null }>({});
  const orderedCellKeys = useRef<string[]>([]);

  const initialOptionalFeeRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      feeTypeId: feeTypeFilter,
      sectionId: 0,
      classId: 0,
    }),
    [adminId, academicYearFilter, feeTypeFilter]
  );
  const currentIndividualFeeSettingRequest = React.useMemo(
    () => ({
      adminId,
      academicId: academicYearFilter,
      feeTypeId: feeTypeFilter,
      sectionId: classSectionsFilter,
      classId: classFilter,
    }),
    [adminId, academicYearFilter, classSectionsFilter, classFilter, feeTypeFilter]
  );

  const loadIndividualFeeList = useCallback(
    async (request: { adminId: number; academicId: number; sectionId: number; classId: number; feeTypeId: number }) => {
      try {
        setSelectedCells([]);
        const data: any = await dispatch(fetchGetOptionalFeeSettingsIndividual(request)).unwrap();
        console.log('Individual fee data::::', data);
        if (data) {
          setOptionalFeeData(data);
          const studentsMap = data.studentsMapped
            ? data.studentsMapped.map((item: GetIndividualFeeStudentMapDataType) => ({ ...item }))
            : [];
          setStudentsData(studentsMap);
          console.log('studentsMap::::', studentsMap);
        }
      } catch (error) {
        console.error('Error loading Individual fee list:', error);
      }
    },
    [dispatch]
  );

  useEffect(() => {
    // If there are NO non-empty values in feeAmounts, disable Save All
    const hasAnyValue = Object.values(feeAmounts).some(
      (val) => val !== '' && val !== undefined && val !== null && val !== 0
    );
    setSaveAllButtonDisabled(!hasAnyValue);
  }, [feeAmounts]);

  React.useEffect(() => {
    // if (individualFeeSettingListStatus === 'idle') {
    //   loadIndividualFeeList(currentIndividualFeeSettingRequest);
    // }
    dispatch(fetchYearList(adminId));
    // dispatch(fetchClassList({ adminId, academicId, sectionId: classSectionsFilter }));
    // dispatch(fetchClassSections({ adminId, academicId }));

    // if (individualFeeSettingListData) {
    //   setOptionalFeeData(individualFeeSettingListData);
    // }
    // console.log('optionalFeeData::::----', optionalFeeData);
    // console.log('selectedCells::::----', selectedCells);
    // console.log('rowId::::----', rowId);
    // console.log('individualSaveButtonEnabled::::----', individualSaveButtonEnabled);
  }, [adminId, dispatch]);

  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId);
    setClassFilter(0);
    setSelectedCells([]);
  };

  const handleFeeTypeChange = (e: SelectChangeEvent) => {
    const feeTypeId = parseInt(e.target.value, 10);
    setFeeTypeFilter(feeTypeId);
    dispatch(fetchClassSections({ adminId, academicId }));
  };

  const handleClassSectionChange = (e: SelectChangeEvent) => {
    setClassSectionsFilter(
      parseInt(ClassSectionsData.filter((item) => item.sectionId === e.target.value)[0].sectionId, 10)
    );
    dispatch(
      fetchClassList({
        adminId,
        academicId,
        sectionId: parseInt(ClassSectionsData.filter((item) => item.sectionId === e.target.value)[0].sectionId, 10),
      })
    );
    setClassFilter(0);
    setSelectedCells([]);
    setFeeAmounts({});
    setClickedCells([]);
  };

  const handleClassChange = (e: SelectChangeEvent) => {
    setClassFilter(parseInt(classListData.filter((item) => item.classId === e.target.value)[0].classId, 10));
    loadIndividualFeeList({ ...currentIndividualFeeSettingRequest, classId: parseInt(e.target.value, 10) });
    setSelectedCells([]);
    setFeeAmounts({});
    setClickedCells([]);
  };

  const selectedIdsArray = String(selectedStudentIds || '')
    .split(',')
    .filter(Boolean)
    .map((id) => Number(id));

  const filteredData =
    selectedIdsArray.length > 0 ? studentsData.filter((row) => selectedIdsArray.includes(row.studentId)) : studentsData;

  useEffect(() => {
    const keys: string[] = [];
    filteredData.forEach((row) => {
      optionalFeeData.optionalFee.forEach((item) => {
        keys.push(`${row.studentId}_${item.feeId}`);
      });
    });
    orderedCellKeys.current = keys;
  }, [filteredData, optionalFeeData]);

  const handleSave = useCallback(
    async (row: GetIndividualFeeStudentMapDataType) => {
      try {
        setSaving(true);
        const { studentId } = row;
        console.log('row::::----', row);
        console.log('rowId state::::----', rowId);
        console.log('feeAmounts state::::----', feeAmounts);

        const allFeeAmntWithStudentIdUpdates: { [key: number]: any[] } = {};

        setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row.studentId]: true }));

        // Construct optionalFee array based on entered amounts
        const filteredKeys = Object.keys(rowId).filter((key) => parseInt(key.split('_')[0], 10) === studentId);
        console.log('Filtered rowId keys for studentId', studentId, ':', filteredKeys);

        const optionalFeeArray = filteredKeys
          .map((key) => {
            const feeId = parseInt(key.split('_')[1], 10);
            const amount = parseFloat(feeAmounts[key]) || 0;
            const existingMapping = row.feeMapped?.find((f) => f.feeId === feeId);

            if (amount === 0) return null; // 🛑 Skip zero amounts

            console.log('Creating optionalFee entry:', {
              key,
              feeId,
              amount,
              optionalMapId: existingMapping?.optionalMapId || 0,
            });

            return {
              feeId,
              amount,
              dbResult: 'string',
              optionalMapId: existingMapping?.optionalMapId || 0,
            };
          })
          .filter((item) => item !== null); // ✅ Remove null entries

        console.log('Final optionalFeeArray (amount > 0):', optionalFeeArray);

        const sendReq = [
          {
            adminId,
            accademicId: academicYearFilter,
            sectionId: classSectionsFilter,
            classId: classFilter,
            studentId,
            feeTypeId: feeTypeFilter,
            optionalFee: optionalFeeArray,
          },
        ];
        const actionResult = await dispatch(optionalFeeSettingsIndividual(sendReq));

        if (actionResult && Array.isArray(actionResult.payload)) {
          const results = actionResult.payload;
          console.log('results::::----', results);
          console.log('fee::::----', feeAmounts);

          const key = `${row.studentId}_`;

          // setFeeAmounts((prev) => ({
          //   ...prev,
          //   [key]: 0,
          // }));

          // setFeeAmounts((prevAmounts) => {
          //   const updatedAmounts = { ...prevAmounts };
          //   delete updatedAmounts[key];
          //   return updatedAmounts;
          // });

          setFeeAmounts((prevAmounts) => {
            const filtered = Object.fromEntries(
              Object.entries(prevAmounts).filter(([k]) => {
                const [studentKey] = k.split('_');
                return parseInt(studentKey, 10) !== studentId; // keep only those NOT matching
              })
            );
            return filtered;
          });

          setClickedCells((prev) => {
            const filtered = prev.filter((cell) => !(cell.studentId === studentId));
            return filtered;
          });

          setSelectedRows((prev) => prev.filter((cell) => cell.studentId !== row.studentId));

          // Filter out items without termAmount property
          const filteredResults = results.filter((f) => f.optionalFee);
          console.log('filteredResults::::----', filteredResults);

          // Extract optionalFee arrays
          const optionalFeeArrays = filteredResults.map((f) => f.optionalFee);

          // Flatten the array of optionalFee arrays
          const optionalFeeArr = optionalFeeArrays.flat();
          console.log('optionalFeeArr::::----', optionalFeeArr);

          // Find the item with dbResult === 'Success'
          const SuccessResult = optionalFeeArr.find((f) => f.dbResult === 'Success');
          console.log('SuccessResult::::----', SuccessResult);
          if (SuccessResult) {
            setSuccesResponse(SuccessResult.dbResult);
          }

          // Find the item with dbResult === 'Updated'
          const UpdateResult = optionalFeeArr.find((f) => f.dbResult === 'Updated');
          console.log('UpdateResult::::----', UpdateResult);
          if (UpdateResult) {
            setSuccesResponse(UpdateResult.dbResult);
          }
          setSaving(false);
          // setTermFeeDetails(response);
          setIndividualSaveLoading((prevMap) => ({ ...prevMap, [row.studentId]: false }));

          const feeAmntWithStudentId = optionalFeeArr.map((item) => ({
            // ...item,
            feeId: item.feeId,
            amount: item.amount,
            dbResult: item.dbResult,
            optionalMapId: item.optionalMapId || 0,
          }));

          // if (!allFeeAmntWithStudentIdUpdates[studentId]) {
          //   allFeeAmntWithStudentIdUpdates[studentId] = [];
          // }
          // allFeeAmntWithStudentIdUpdates[studentId] = [
          //   ...allFeeAmntWithStudentIdUpdates[studentId],
          //   ...feeAmntWithStudentId,
          // ];

          // if (SuccessResult) {
          //   // Update feeMapped for the current row
          //   const updatedOptionalFeeData = optionalFeeData.map((item) => {
          //     if (item.studentId === studentId) {
          //       const feeMappedArr = Array.isArray(item.feeMapped) ? item.feeMapped : [];
          //       const updatedFeeMapped = [...feeMappedArr, ...feeAmntWithStudentId];

          //       console.log('feeAmntWithStudentId1st::::----', feeAmntWithStudentId); // Correct placement of console.log
          //       return { ...item, feeMapped: updatedFeeMapped };
          //     }
          //     return item;
          //   });

          //   setOptionalFeeData(updatedOptionalFeeData);
          // }

          // // Update termFeeMapped for the current row
          // const updatedOptionalFeeData = optionalFeeData.map((item) => {
          //   if (item.studentId === studentId) {
          //     // Push each item of optionalFeeArr separately to feeMapped
          //     const updatedfeeMapped = [...item.feeMapped, ...optionalFeeArr];
          //     return { ...item, feeMapped: updatedfeeMapped };
          //   }
          //   return item;
          // });
          // setOptionalFeeData(updatedOptionalFeeData);
          // console.log('updatedOptionalFeeData::::----', updatedOptionalFeeData);

          // Update checkedRows when the row is unchecked
          // const updatedCheckedRows = { ...checkedRows };
          // if (checkedRows[row.feeId]) {
          //   delete updatedCheckedRows[row.feeId];
          // }
          // setCheckedRows(updatedCheckedRows);

          loadIndividualFeeList({
            ...currentIndividualFeeSettingRequest,
            academicId: academicYearFilter,
            adminId,
            sectionId: classSectionsFilter,
            classId: classFilter,
          });
          setShowSuccessIcon((prevMap) => ({ ...prevMap, [row.studentId]: true }));
          setIndividualSaveButtonEnabled((prevMap) => ({ ...prevMap, [row.studentId]: false }));
          setTimeout(() => {
            setShowSuccessIcon((prevMap) => ({ ...prevMap, [row.studentId]: false }));
          }, 5000);
        }

        // const updatedOptionalFeeData = optionalFeeData.map((item) => {
        //   if (allFeeAmntWithStudentIdUpdates[item.studentId] && Array.isArray(item.feeMapped)) {
        //     const updatedTermFeeMapped = [...item.feeMapped, ...allFeeAmntWithStudentIdUpdates[item.studentId]];
        //     return { ...item, feeMapped: updatedTermFeeMapped };
        //   }
        //   return item;
        // });
        // setOptionalFeeData(updatedOptionalFeeData);
      } catch (error) {
        // Handle errors here
        // await showConfirmation(<ErrorMessage icon={ErrorMsg} message="Message content already created" />, '');
      }
    },
    [
      dispatch,
      adminId,
      academicYearFilter,
      classSectionsFilter,
      classFilter,
      rowId,
      feeAmounts,
      feeTypeFilter,
      optionalFeeData,
      loadIndividualFeeList,
      currentIndividualFeeSettingRequest,
    ]
  );
  const handleAllSave = useCallback(async () => {
    try {
      setSavingAll(true);

      const feeIdArray = Object.keys(rowId).map((key) => parseInt(key.split('_')[0], 10));

      // Ensure feeIdArray contains unique values
      const uniqueFeeIds = [...new Set(feeIdArray)];
      await Promise.all(
        uniqueFeeIds.map(async (studentId) => {
          // Construct optionalFee array based on entered amounts
          // const optionalFeeArray = Object.keys(rowId)
          //   .filter((key) => parseInt(key.split('_')[0], 10) === studentId) // Filter objects where studentId matches
          //   .map((key) => {
          //     const feeId = parseInt(key.split('_')[1], 10);
          //     const amount = parseFloat(feeAmounts[key]) || 0;

          //     return {
          //       feeId,
          //       amount,
          //       dbResult: 'string',
          //       optionalMapId: 0,
          //     };
          //   });

          const optionalFeeArray = Object.keys(rowId)
            .filter((key) => parseInt(key.split('_')[0], 10) === studentId) // Filter objects where studentId matches
            .map((key) => {
              const feeId = parseInt(key.split('_')[1], 10);
              const amount = parseFloat(feeAmounts[key]) || 0;

              if (amount === null || amount === 0) {
                return null;
              }

              return {
                feeId,
                amount,
                dbResult: 'string',
                optionalMapId: 0,
              };
            })
            .filter(Boolean); // Filters out null values

          const sendReq = [
            {
              adminId,
              accademicId: academicYearFilter,
              sectionId: classSectionsFilter,
              classId: classFilter,
              studentId,
              feeTypeId: feeTypeFilter,
              optionalFee: optionalFeeArray,
            },
          ];
          const actionResult = await dispatch(optionalFeeSettingsIndividual(sendReq));

          if (actionResult && Array.isArray(actionResult.payload)) {
            setSnackBar(true);
            const results = actionResult.payload;
            setClickedCells([]);
            setSelectedRows([]);
            console.log('results::::----', results);
            // Filter out items without termAmount property
            const filteredResults = results.filter((f) => f.optionalFee);
            console.log('filteredResults::::----', filteredResults);

            // Extract optionalFee arrays
            const optionalFeeArrays = filteredResults.map((f) => f.optionalFee);

            // Flatten the array of optionalFee arrays
            const optionalFeeArr = optionalFeeArrays.flat();
            console.log('optionalFeeArr::::----', optionalFeeArr);

            // Find the item with dbResult === 'Success'
            const SuccessResult = optionalFeeArr.find((f) => f.dbResult === 'Success');
            console.log('SuccessResult::::----', SuccessResult);
            if (SuccessResult) {
              setSuccesResponse(SuccessResult.dbResult);
            }

            // Find the item with dbResult === 'Updated'
            const UpdateResult = optionalFeeArr.find((f) => f.dbResult === 'Updated');
            console.log('UpdateResult::::----', UpdateResult);
            if (UpdateResult) {
              setSuccesResponse(UpdateResult.dbResult);
            }
            setSavingAll(false);
            setIndividualSaveButtonEnabled([]);
            setSaveAllButtonDisabled(true);

            // // Update termFeeMapped for the current row
            // const updatedOptionalFeeData = optionalFeeData.map((item) => {
            //   if (item.studentId === studentId) {
            //     // Push each item of optionalFeeArr separately to feeMapped
            //     const updatedfeeMapped = [...item.feeMapped, ...optionalFeeArr];
            //     return { ...item, feeMapped: updatedfeeMapped };
            //   }
            //   return item;
            // });
            // setOptionalFeeData(updatedOptionalFeeData);
            // console.log('updatedOptionalFeeData::::----', updatedOptionalFeeData);

            // Update checkedRows when the row is unchecked
            // const updatedCheckedRows = { ...checkedRows };
            // if (checkedRows[row.feeId]) {
            //   delete updatedCheckedRows[row.feeId];
            // }
            // setCheckedRows(updatedCheckedRows);

            loadIndividualFeeList({
              ...currentIndividualFeeSettingRequest,
              academicId: academicYearFilter,
              adminId,
              sectionId: classSectionsFilter,
              classId: classFilter,
            });
          }
        })
      );
    } catch (error) {
      // Handle errors here
      // await showConfirmation(<ErrorMessage icon={ErrorMsg} message="Message content already created" />, '');
    }
  }, [
    dispatch,
    adminId,
    academicYearFilter,
    classSectionsFilter,
    classFilter,
    rowId,
    feeAmounts,
    currentIndividualFeeSettingRequest,
    loadIndividualFeeList,
    feeTypeFilter,
  ]);

  // const handleCellClick = useCallback(
  //   async (studentId: number, feeId: number) => {
  //     const cellKey: any = `${studentId}_${feeId}`;

  //     // Check if cell is already selected
  //     const cellIndex = selectedCells.findIndex((cell) => cell.studentId === studentId && cell.feeId === feeId);

  //     if (cellIndex !== -1) {
  //       // Cell a lready selected, deselect it
  //       setSelectedCells((prevSelectedCells) =>
  //         prevSelectedCells.filter((cell) => !(cell.studentId === studentId && cell.feeId === feeId))
  //       );
  //       setRowId((prev) => {
  //         const updatedIds: RowIds = { ...prev };
  //         delete updatedIds[cellKey];
  //         return updatedIds;
  //       });
  //       setIndividualSaveButtonEnabled((prev: any) => {
  //         const updatedEnabled: EnabledState = { ...prev };
  //         if (updatedEnabled[studentId]) {
  //           updatedEnabled[studentId] = updatedEnabled[studentId].filter((key) => key !== cellKey);
  //           if (updatedEnabled[studentId].length === 0) {
  //             delete updatedEnabled[studentId]; // Remove studentId array if it becomes empty
  //           }
  //         }
  //         if (updatedEnabled) {
  //           const isSaveAllButtonDisabled = Object.keys(updatedEnabled).length === 0;
  //           setSaveAllButtonDisabled(isSaveAllButtonDisabled);
  //         }
  //         return updatedEnabled;
  //       });
  //     } else {
  //       setSelectedCells((prevSelectedCells) => [...prevSelectedCells, { studentId, feeId }]);
  //       // Cell not selected, select it
  //       setRowId((prev) => {
  //         const updatedIds: RowIds = { ...prev };
  //         updatedIds[cellKey] = cellKey;
  //         return updatedIds;
  //       });
  //       setIndividualSaveButtonEnabled((prevEnabled: any) => {
  //         const updatedEnabled = { ...prevEnabled };
  //         if (!updatedEnabled[studentId]) {
  //           updatedEnabled[studentId] = []; // Initialize as an empty array if not exists
  //         }
  //         if (!updatedEnabled[studentId].includes(cellKey)) {
  //           updatedEnabled[studentId].push(cellKey); // Add cellKey to the array
  //           if (updatedEnabled) {
  //             const isSaveAllButtonDisabled = Object.keys(updatedEnabled).length === 0;
  //             setSaveAllButtonDisabled(isSaveAllButtonDisabled);
  //           }
  //         }
  //         return updatedEnabled;
  //       });
  //       // Enable the Save All button
  //     }
  //     // Check if any cells are selected
  //   },
  //   [setSelectedCells, selectedCells]
  // );

  const handleAmountChange = useCallback(
    (
      e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
      row: GetIndividualFeeStudentMapDataType,
      feeId: number,
      id: number
    ) => {
      const { studentId } = row;
      const isSelectedRow = selectedRows.includes(row);
      const cellKey: string = `${studentId}_${feeId}`;

      const newValue = parseInt(e.target.value, 10);
      const newValueString = e.target.value;

      setAmountEnter(newValue || newValueString);
      setEnteredValues((prevValues) => [...prevValues, newValue]);
      setEnteredCells((prevCells) => ({ ...prevCells, [cellKey]: newValue }));

      console.log('enteredValues::::----', enteredValues);
      console.log('feeAmounts::::----', feeAmounts);

      const feeMapped = Array.isArray(row?.feeMapped) ? row?.feeMapped : [];
      const amountObj = feeMapped.find((f) => f.feeId === feeId);
      const amount = amountObj ? amountObj.amount : 0;

      // Update the enabled state for the corresponding row
      const isZeroOrEmpty = newValue === 0 || newValueString === '';
      setIndividualSaveButtonEnabled((prevEnabled: any) => ({
        ...prevEnabled,
        [`${studentId}`]: !isZeroOrEmpty,
      }));

      // Update the feeAmounts array based on the new value
      setFeeAmounts((prevAmounts) => {
        const updatedAmounts = { ...prevAmounts };
        if (newValue === 0 || Number.isNaN(newValue)) {
          if (isSelectedRow && id === 1) {
            updatedAmounts[cellKey] = amount;
          } else {
            delete updatedAmounts[cellKey];
          }
          setCellErrorMessages((prev) => ({
            ...prev,
            [cellKey]: '',
          }));
        } else if (newValue !== 0 || !Number.isNaN(newValue)) {
          updatedAmounts[cellKey] = newValue;
          // Add validation if needed
          setCellErrorMessages((prev) => ({
            ...prev,
            [cellKey]: '',
          }));
        } else {
          updatedAmounts[cellKey] = amount;
        }

        return updatedAmounts;
      });

      // Enable save all button
      setSaveAllButtonDisabled(false);

      // Update rowId for tracking
      setRowId((prev) => ({
        ...prev,
        [cellKey]: feeId,
      }));
    },
    [
      setAmountEnter,
      setEnteredValues,
      feeAmounts,
      selectedRows,
      setIndividualSaveButtonEnabled,
      enteredValues,
      setFeeAmounts,
    ]
  );

  const handleCellClick = useCallback(
    (row: any, feeId: number) => {
      const { studentId } = row;
      const key = `${studentId}_${feeId}`;

      setClickedCells((prevClickedCells) => [...prevClickedCells, { studentId, feeId, value: '' }]);

      setFocusedCellKey(key);

      console.log('focusedCellKey::::----', focusedCellKey);

      // Scroll the input into view after a short delay (to let the DOM update)
      requestAnimationFrame(() => {
        const el = inputRef.current?.[key];
        if (el) {
          el.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      });
    },
    [setClickedCells, focusedCellKey]
  );

  const handleEditCell = useCallback(
    (row: GetIndividualFeeStudentMapDataType, feeId: number) => {
      const { studentId } = row;
      const cellKey = `${studentId}_${feeId}`;

      // Get existing amount to pre-populate the input
      const monthData = row?.feeMapped && row?.feeMapped.find((data) => data.feeId === feeId);
      const existingAmount = monthData ? monthData.amount : 0;

      // Initialize feeAmounts with existing value if not already set
      setFeeAmounts((prevAmounts) => {
        if (!prevAmounts[cellKey] && existingAmount) {
          return {
            ...prevAmounts,
            [cellKey]: existingAmount,
          };
        }
        return prevAmounts;
      });

      // Update rowId to mark this cell as modified (crucial for save functionality)
      setRowId((prev) => ({
        ...prev,
        [cellKey]: feeId,
      }));

      console.log('Edit cell - updating rowId with key:', cellKey, 'feeId:', feeId);

      // Enable individual save button for this student
      setIndividualSaveButtonEnabled((prevEnabled: any) => ({
        ...prevEnabled,
        [`${studentId}`]: true,
      }));

      // Enable save all button
      setSaveAllButtonDisabled(false);

      // Add this cell to clicked cells to show the input field
      setClickedCells((prevClickedCells) => {
        const isAlreadyClicked = prevClickedCells.some((cell) => cell.studentId === studentId && cell.feeId === feeId);

        if (!isAlreadyClicked) {
          return [...prevClickedCells, { studentId, feeId, value: '' }];
        }
        return prevClickedCells;
      });

      // Focus the input field after a short delay to ensure it's rendered
      setTimeout(() => {
        const inputElement = inputRef.current[cellKey];
        if (inputElement) {
          inputElement.focus();
          // inputElement.select(); // Select all text for easy editing
        }
      }, 100);
    },
    [setClickedCells]
  );

  const handleDeleteCell = useCallback(
    async (optionalMapId: number) => {
      const sendConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={
            <div style={{ color: theme.palette.error.main }}>
              Are you sure you want to delete the cell &quot;{}&quot; ?
            </div>
          }
        />
      );

      if (await confirm(sendConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = [
          {
            adminId,
            accademicId: academicYearFilter,
            optionalMapId,
            dbResult: '',
            deletedId: 0,
          },
        ];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(DeleteOptionalFee(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Deleted');

          if (!errorMessages) {
            const deleteSuccessMessage = (
              <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete successfully" />
            );
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
            loadIndividualFeeList(currentIndividualFeeSettingRequest);
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
          loadIndividualFeeList(currentIndividualFeeSettingRequest);
        }
      }
    },
    [confirm, dispatch, adminId, academicYearFilter, theme, loadIndividualFeeList, currentIndividualFeeSettingRequest]
  );

  const handleDeleteRow = useCallback(
    async (row: GetIndividualFeeStudentMapDataType) => {
      const { studentId } = row;
      console.log('row::::----', row);
      // const fineMappedIds = row.filter((f) => f.termId === term.termId).map((m) => m.fineMappedId);

      const sendConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={
            <div style={{ color: theme.palette.error.main }}>
              Are you sure you want to delete the row &quot;{}&quot; ?
            </div>
          }
        />
      );

      if (await confirm(sendConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = [
          {
            adminId,
            accademicId: academicYearFilter,
            classId: classFilter,
            studentId,
            dbResult: 'string',
            deletedId: 0,
          },
        ];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(DeleteAllOptionalFee(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Success');

          if (!errorMessages) {
            const deleteSuccessMessage = (
              <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete successfully" />
            );
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
            loadIndividualFeeList(currentIndividualFeeSettingRequest);
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
          loadIndividualFeeList(currentIndividualFeeSettingRequest);
        }
      }
    },
    [
      confirm,
      dispatch,
      adminId,
      academicYearFilter,
      classFilter,
      theme,
      loadIndividualFeeList,
      currentIndividualFeeSettingRequest,
    ]
  );

  const handleDeleteMultiple = useCallback(async () => {
    const sendConfirmMessage = (
      <DeleteMessage jsonIcon={deleteBin} message={<div>Are you sure you want to delete all ?</div>} />
    );
    if (await confirm(sendConfirmMessage, 'Delete Basic Fee?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
      // const deleteResponse = await dispatch(deleteBasicFeeList(sendReq));
      const sendRequests: OptionalFeeMappedDeleteAllType[] = await Promise.all(
        selectedRows?.map(async (row) => {
          const sendReq = {
            adminId,
            accademicId: academicYearFilter,
            classId: classFilter,
            studentId: row.studentId,
            dbResult: '',
            deletedId: 0,
          };
          return sendReq;
        }) || []
      );
      const deleteResponse = await dispatch(DeleteAllOptionalFee(sendRequests));

      console.log('deleteResponse', deleteResponse);
      if (deleteResponse && Array.isArray(deleteResponse.payload)) {
        const results: OptionalFeeMappedDeleteAllType[] = deleteResponse.payload;
        const errorMessages = results.find((result) => result.dbResult === 'Failed');
        const successMessages = results.find((result) => result.dbResult === 'Success');

        if (!errorMessages) {
          const deleteSuccessMessage = (
            <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete All Successfully" />
          );
          await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
          loadIndividualFeeList(currentIndividualFeeSettingRequest);
        } else if (!successMessages) {
          const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete Failed" />;
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          const deleteErrorMessage = (
            <DeleteMessage loop={false} jsonIcon={errorIcon} message="Something went wrong please try again" />
          );
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        }
        loadIndividualFeeList(currentIndividualFeeSettingRequest);
        setSelectedRows([]);
        // setTermFeeDetails((prevDetails) => prevDetails.filter((item) => !selectedRows.includes(item)));
      }
    }
  }, [
    confirm,
    dispatch,
    adminId,
    selectedRows,
    academicYearFilter,
    classFilter,
    loadIndividualFeeList,
    currentIndividualFeeSettingRequest,
  ]);

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      loadIndividualFeeList(initialOptionalFeeRequest);
      setAcademicYearFilter(0);
      setClassSectionsFilter(0);
      setFeeTypeFilter(0);
      setClassFilter(0);
      setSelectedStudentIds('');
    },
    [initialOptionalFeeRequest, loadIndividualFeeList]
  );

  const isSelected = (row: GetIndividualFeeStudentMapDataType) => selectedRows?.indexOf(row) !== -1;

  const handleRowClick = (row: GetIndividualFeeStudentMapDataType) => {
    // Check if the clicked row is disabled
    const rowIndex = filteredData.findIndex((item) => item === row);

    const selectedIndex = selectedRows?.indexOf(row);
    let newSelected: any[] = [];

    if (selectedIndex === -1) {
      newSelected = newSelected.concat(selectedRows, row);
    } else if (selectedIndex === 0) {
      newSelected = newSelected.concat(selectedRows.slice(1));
    } else if (selectedIndex === selectedRows.length - 1) {
      newSelected = newSelected.concat(selectedRows.slice(0, -1));
    } else if (selectedIndex > 0) {
      newSelected = newSelected.concat(selectedRows.slice(0, selectedIndex), selectedRows.slice(selectedIndex + 1));
    }

    setSelectedRows(newSelected);
  };
  const handleRowAllClick = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      setSelectedRows(filteredData);
    } else {
      setSelectedRows([]);
    }
  };

  const IndividualFeeSettingListColumns: DataTableColumn<GetIndividualFeeStudentMapDataType>[] = useMemo(() => {
    const baseColumns: DataTableColumn<any>[] = [
      {
        name: 'checkBox',
        renderHeader: () => {
          return (
            <Checkbox
              onChange={handleRowAllClick}
              indeterminate={selectedRows?.length > 0 && selectedRows?.length < studentsData.length}
              checked={selectedRows?.length > 0}
            />
          );
        },
        renderCell: (row) => {
          const isSelectedRow = isSelected(row);
          const disabledCheckBox = row.feeMapped?.length > 0;
          return <Checkbox checked={isSelectedRow} disabled={!disabledCheckBox} onClick={() => handleRowClick(row)} />;
        },
      },
      {
        name: 'slNo',
        renderHeader: () => (
          <Typography sx={{ width: 100 }} variant="subtitle2" fontSize={13}>
            Admission No
          </Typography>
        ),
        headerLabel: '',
        renderCell: (row) => {
          return (
            <Typography width={100} variant="subtitle1" fontSize={13}>
              {row.admissionNo}
            </Typography>
          );
        },
        sortable: true,
      },
      {
        name: 'studentName',
        renderHeader: () => (
          <Typography width={200} variant="subtitle2" fontSize={13}>
            Student Name
          </Typography>
        ),
        renderCell: (row) => {
          return (
            <Stack width={200} pl={1} direction="row" alignItems="center" gap={1}>
              <Avatar src="" />
              <Typography variant="subtitle2" fontSize={13}>
                {row.studentName}
              </Typography>
            </Stack>
          );
        },
      },
    ];

    const headerAndBodyCells = optionalFeeData.optionalFee
      ? optionalFeeData.optionalFee.map((item: { feeId: number; feeTitle: string }) => ({
          name: `${item.feeId}`,
          // width: '80px',
          renderHeader: () => (
            <Stack minWidth={110} key={item.feeId} className="header-color" direction="row" justifyContent="center">
              <Typography variant="subtitle2" fontSize={12}>
                {item.feeTitle}
              </Typography>
            </Stack>
          ),
          renderCell: (row: GetIndividualFeeStudentMapDataType) => {
            const isCellClicked = clickedCells.some(
              (cell) => cell.studentId === row?.studentId && cell.feeId === item.feeId
            );
            const isSelectedRow = selectedRows.includes(row) && !isCellClicked;
            const key = `${row?.studentId}_${item.feeId}`;
            const monthData = row?.feeMapped?.find((data) => data.feeId === item.feeId);
            const amount = monthData?.amount ?? '';

            return (
              <Stack className={isSelectedRow ? 'inputCellActive' : 'inputCellInActive'}>
                {isSelectedRow && monthData ? (
                  // ✅ Case 1: Row is selected and has data — Editable field
                  <Stack
                    key={key}
                    direction="row"
                    alignItems="center"
                    justifyContent="center"
                    className="cellActive"
                    minWidth={110}
                    height={60}
                    pl={1}
                    sx={{ borderLeft: 3 }}
                    position="relative"
                  >
                    <CurrencyRupeeIcon sx={{ fontSize: '14px' }} color="warning" />
                    <TextField
                      type="number"
                      variant="outlined"
                      size="small"
                      sx={{
                        '& fieldset': { border: '1px' },
                        '& .MuiOutlinedInput-root': {
                          padding: 0,
                        },
                        '& .MuiOutlinedInput-input': {
                          padding: '0px 0px', // adjust as needed, or set to 0 for full removal
                          minWidth: 50,
                          fontSize: 12,
                          fontWeight: 600,
                        },
                      }}
                      inputProps={{
                        maxLength: 6,
                        style: {
                          minWidth: 50,
                          fontSize: 12,
                          fontWeight: 600,
                        },
                      }}
                      // value={feeAmounts[key] ?? ''}
                      defaultValue={amount}
                      value={feeAmounts[key]}
                      onChange={(e) => handleAmountChange(e, row, item.feeId, 0)}
                      inputRef={(el) => {
                        if (el) {
                          inputRef.current[key] = el;
                          if (focusedCellKey === key) {
                            requestAnimationFrame(() => {
                              el.focus();
                              // el.select();
                            });
                          }
                        }
                      }}
                      onFocus={() => {
                        if (focusedCellKey !== key) {
                          setFocusedCellKey(key);
                        }
                      }}
                      onKeyDown={(event) => {
                        if (event.key === 'Enter') {
                          event.preventDefault();

                          const currentKey = key;
                          const index = orderedCellKeys.current.indexOf(currentKey);

                          if (index !== -1 && index + 1 < orderedCellKeys.current.length) {
                            const nextKey = orderedCellKeys.current[index + 1];
                            const [nextStudentIdStr, nextFeeIdStr] = nextKey.split('_');
                            const nextStudentId = Number(nextStudentIdStr);
                            const nextFeeId = Number(nextFeeIdStr);

                            // 1. Open the next cell
                            setClickedCells((prev) => {
                              const alreadyOpen = prev.some(
                                (cell) => cell.studentId === nextStudentId && cell.feeId === nextFeeId
                              );
                              if (alreadyOpen) return prev;
                              return [...prev, { studentId: nextStudentId, feeId: nextFeeId }];
                            });

                            // 2. Set focus to the next input once it's rendered
                            setFocusedCellKey(nextKey);
                          }
                        }
                      }}
                      // onBlur={() => {
                      //   setClickedCells((prev) => prev.filter((cell) => `${cell.studentId}_${cell.feeId}` !== key));
                      //   setFocusedCellKey(null);
                      // }}
                    />
                  </Stack>
                ) : isCellClicked ? (
                  // ✅ Case 2: Cell was clicked manually
                  <Stack
                    direction="row"
                    alignItems="center"
                    justifyContent="center"
                    minWidth={110}
                    height={60}
                    pl={1}
                    position="relative"
                    sx={{ borderLeft: monthData && 3 }}
                    className={monthData && 'cellActive'}
                  >
                    <CurrencyRupeeIcon sx={{ fontSize: '14px' }} color={monthData && 'warning'} />
                    <TextField
                      type="number"
                      variant="outlined"
                      size="small"
                      sx={{
                        '& fieldset': { border: '1px', p: 0 },
                        '& .MuiOutlinedInput-root': {
                          padding: 0,
                        },
                        '& .MuiOutlinedInput-input': {
                          padding: '0px 0px', // adjust as needed, or set to 0 for full removal
                          minWidth: 50,
                          fontSize: 12,
                          fontWeight: 600,
                        },
                      }}
                      inputProps={{
                        maxLength: 6,
                        style: {
                          minWidth: 50,
                          fontSize: 12,
                          fontWeight: 600,
                        },
                      }}
                      // autoFocus={focusedCellKey === key}
                      // inputRef={(el) => {
                      //   if (!inputRef.current[key]) {
                      //     inputRef.current[key] = el;
                      //   }
                      // }}
                      inputRef={(el) => {
                        if (el) {
                          inputRef.current[key] = el;
                          if (focusedCellKey === key) {
                            requestAnimationFrame(() => {
                              el.focus();
                              // el.select();
                            });
                          }
                        }
                      }}
                      onFocus={() => {
                        if (focusedCellKey !== key) {
                          setFocusedCellKey(key);
                        }
                      }}
                      // onBlur={() => {
                      //   setClickedCells((prev) => prev.filter((cell) => `${cell.studentId}_${cell.feeId}` !== key));
                      //   setFocusedCellKey(null);
                      // }}
                      // onKeyPress={(event) => {
                      //   if (event.key === 'Enter') {
                      //     event.preventDefault();

                      //     // Current cell identification
                      //     const currentKey = key;
                      //     const allKeys = Object.keys(inputRef.current);
                      //     const currentIndex = allKeys.indexOf(currentKey);
                      //     // const [fId, tId] = nextKey.split('_').map(Number); // Convert strings to numbers

                      //     setClickedCells((prevClickedCells) => [
                      //       ...prevClickedCells,
                      //       { studentId: row.studentId, feeId: item.feeId + 1 },
                      //     ]);
                      //     if (currentIndex !== -1 && currentIndex + 1 < allKeys.length) {
                      //       const nextKey = allKeys[currentIndex + 1];
                      //       // Move focus to the next cell

                      //       // Move focus to the next cell
                      //       inputRef.current[nextKey]?.focus();
                      //       console.log('c::::----', clickedCells);
                      //     }
                      //   }
                      // }}
                      onKeyDown={(event) => {
                        if (event.key === 'Enter') {
                          event.preventDefault();

                          const currentKey = key;
                          const index = orderedCellKeys.current.indexOf(currentKey);

                          if (index !== -1 && index + 1 < orderedCellKeys.current.length) {
                            const nextKey = orderedCellKeys.current[index + 1];
                            const [nextStudentIdStr, nextFeeIdStr] = nextKey.split('_');
                            const nextStudentId = Number(nextStudentIdStr);
                            const nextFeeId = Number(nextFeeIdStr);

                            // 1. Open the next cell
                            setClickedCells((prev) => {
                              const alreadyOpen = prev.some(
                                (cell) => cell.studentId === nextStudentId && cell.feeId === nextFeeId
                              );
                              if (alreadyOpen) return prev;
                              return [...prev, { studentId: nextStudentId, feeId: nextFeeId }];
                            });

                            // 2. Set focus to the next input once it's rendered
                            setFocusedCellKey(nextKey);
                          }
                        }
                      }}
                      // onKeyDown={(event) => {
                      //   if (event.key === 'Enter') {
                      //     event.preventDefault();

                      //     const currentKey = key;
                      //     const index = orderedCellKeys.current.indexOf(currentKey);

                      //     // Search for the next editable cell (without monthData)
                      //     for (let i = index + 1; i < orderedCellKeys.current.length; i += 1) {
                      //       const nextKey = orderedCellKeys.current[i];
                      //       const [nextStudentIdStr, nextFeeIdStr] = nextKey.split('_');
                      //       const nextStudentId = Number(nextStudentIdStr);
                      //       const nextFeeId = Number(nextFeeIdStr);

                      //       // ✅ Find matching row and check if next cell has monthData
                      //       const nextRow = filteredData.find((r) => r.studentId === nextStudentId);
                      //       const hasMonthData = nextRow?.feeMapped?.some((f) => f.feeId === nextFeeId);

                      //       if (!hasMonthData) {
                      //         // ✅ Open the cell
                      //         setClickedCells((prev) => {
                      //           const alreadyOpen = prev.some(
                      //             (cell) => cell.studentId === nextStudentId && cell.feeId === nextFeeId
                      //           );
                      //           return alreadyOpen ? prev : [...prev, { studentId: nextStudentId, feeId: nextFeeId }];
                      //         });

                      //         // ✅ Focus after rendering
                      //         setFocusedCellKey(nextKey);
                      //         break; // Exit loop after first editable cell found
                      //       }
                      //     }
                      //   }
                      // }}
                      value={feeAmounts[key] ?? ''}
                      onChange={(e) => handleAmountChange(e, row, item.feeId, 0)}
                      disabled={isSelectedRow}
                    />
                    <IconButton
                      size="small"
                      onClick={() => {
                        // setIndividualSaveButtonEnabled((prevMap) => ({ ...prevMap, [row.studentId]: false }));
                        setClickedCells((prev) => prev.filter((cell) => `${cell.studentId}_${cell.feeId}` !== key));
                        setFeeAmounts((prev) => {
                          const { [key]: removed, ...rest } = prev;
                          return rest;
                        });
                        // Check if all cells for this row are now empty
                        setTimeout(() => {
                          setFeeAmounts((prev) => {
                            // Get all keys for this studentId
                            const hasAny = Object.keys(prev).some(
                              (k) => k.startsWith(`${row.studentId}_`) && prev[k] !== '' && prev[k] !== undefined
                            );
                            if (!hasAny) {
                              setIndividualSaveButtonEnabled((prevEnabled) => ({
                                ...prevEnabled,
                                [`${row.studentId}`]: false,
                              }));
                            }
                            return prev;
                          });
                        }, 0);
                      }}
                      sx={{ position: 'relative', right: 5 }}
                    >
                      <CloseIcon sx={{ fontSize: 16 }} />
                    </IconButton>
                  </Stack>
                ) : monthData ? (
                  // ✅ Case 3: Has data but not selected — show readonly with Edit/Delete
                  <Stack
                    direction="row"
                    alignItems="center"
                    key={`cell_${key}`}
                    className="cellInActive"
                    sx={{ borderLeft: 3 }}
                    color="#000"
                    // gap={2}
                    pl={1}
                    height={60}
                    minWidth={110}
                    position="relative"
                  >
                    <CurrencyRupeeIcon sx={{ fontSize: 14 }} color="success" />
                    <Typography fontSize={12} variant="subtitle2">
                      {amount}
                    </Typography>
                    <Stack position="absolute" top={2} right={2}>
                      <IconButton onClick={() => handleEditCell(row, item.feeId)} size="small">
                        <EditIcon sx={{ fontSize: 16 }} />
                      </IconButton>
                      <IconButton onClick={() => handleDeleteCell(monthData?.optionalMapId)} size="small">
                        <DeleteIcon sx={{ fontSize: 16 }} />
                      </IconButton>
                    </Stack>
                  </Stack>
                ) : (
                  // ✅ Case 4: No data yet — show Add Icon
                  <Stack
                    height={60}
                    overflow="hidden"
                    className={isSelectedRow ? 'inputCellActive' : 'inputCellInActive'}
                    // onClick={() => {
                    //   if (!isSelectedRow) {
                    //     setClickedCells((prev) => [
                    //       ...prev.filter((c) => `${c.studentId}_${c.feeId}` !== key),
                    //       { studentId: row.studentId, feeId: item.feeId },
                    //     ]);
                    //     setFocusedCellKey(key);
                    //   }
                    // }}
                    onClick={() => !isSelectedRow && handleCellClick(row, item.feeId)}
                    sx={{
                      width: '100%',
                      transform: 'scale(0.9)',
                      transition: '.1s',
                      '&:hover': {
                        '.hover-icon': {
                          transform: !isSelectedRow ? 'scale(1.2)' : '',
                          transition: !isSelectedRow ? '.1s' : '',
                          color: !isSelectedRow ? theme.palette.success.main : '',
                        },
                      },
                    }}
                    direction="row"
                    justifyContent="center"
                    alignItems="center"
                  >
                    <AddIcon className="hover-icon" sx={{ transition: 'opacity 0.2s ease-in-out' }} />
                  </Stack>
                )}
              </Stack>
            );
          },
        }))
      : [];

    const baseColumns2: DataTableColumn<any>[] = [
      // {
      //   name: 'total',
      //   headerLabel: 'Total',
      //   renderCell: (row, index) => (
      //     <Typography px={1} textAlign="start" variant="subtitle1" fontSize={13}>
      //       {calculateTotals(row, index)}
      //     </Typography>
      //   ),
      // },
      {
        name: '',
        renderHeader: () => (
          <Typography width={80} textAlign="start" className="header-color" variant="subtitle2" fontSize={14}>
            Actions
          </Typography>
        ),
        renderCell: (row) => {
          return (
            <Stack width={80} direction="row" alignItems="center" justifyContent="center" gap={1}>
              {
                showSuccessIcon[row.studentId] === true ? (
                  <Stack direction="row" justifyContent="center" flexDirection="column" alignItems="center">
                    {succesResponse === 'Success' ? (
                      <>
                        <Lottie animationData={Success} loop={false} style={{ width: '30px' }} />
                        <Typography color={theme.palette.success.main} fontSize={7} variant="subtitle2">
                          Saved
                        </Typography>
                      </>
                    ) : (
                      <>
                        <Lottie animationData={Updated} loop={false} style={{ width: '30px' }} />
                        <Typography color={theme.palette.warning.main} fontSize={7} variant="subtitle2">
                          Updated
                        </Typography>
                      </>
                    )}
                  </Stack>
                ) : showErrorIcon[row.studentId] === true ? (
                  <Stack direction="row" justifyContent="center">
                    <Lottie animationData={errorIcon} loop={false} style={{ width: '30px' }} />
                  </Stack>
                ) : !individualSaveLoading[row.studentId] === true ? (
                  <IconButton
                    // disabled={!individualSaveButtonEnabled.hasOwnProperty(row.studentId) || false}
                    disabled={!individualSaveButtonEnabled[row?.studentId]}
                    size="small"
                    color={checkedRows[row.studentId] ? 'warning' : 'success'}
                    aria-label=""
                    onClick={() => handleSave(row)}
                  >
                    <SaveIcon fontSize="small" />
                  </IconButton>
                ) : (
                  <Stack direction="row" justifyContent="center" flexDirection="column" alignItems="center">
                    <>
                      <Lottie
                        animationData={checkedRows[row.studentId] ? UpdateLoading : SaveLoading}
                        loop
                        style={{ width: '20px' }}
                      />
                      <Typography
                        color={checkedRows[row.studentId] ? theme.palette.warning.main : theme.palette.success.main}
                        fontSize={7}
                        variant="subtitle2"
                      >
                        {checkedRows[row.studentId] ? 'Updating...' : 'Saving...'}
                      </Typography>
                    </>
                  </Stack>
                )
                // <LoadingButton
                //   loading={individualSaveLoading[row.feeId]}
                //   onClick={() => handleSave(row)}
                //   variant="contained"
                //   size="small"
                //   color="success"
                //   disabled={!individualSaveButtonEnabled[row.feeId] || saving}
                //   sx={{ py: 0.5, fontSize: '10px' }}
                // >
                //   {!individualSaveLoading[row.feeId] ? 'Save' : ''}
                // </LoadingButton>
              }

              <IconButton
                disabled={row.feeMapped === null}
                onClick={() => handleDeleteRow(row)}
                size="small"
                color="error"
                aria-label=""
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Stack>
          );
        },
      },
    ];

    return [...baseColumns, ...headerAndBodyCells, ...baseColumns2];
  }, [
    theme,
    handleCellClick,
    handleAmountChange,
    optionalFeeData,
    individualSaveButtonEnabled,
    showErrorIcon,
    checkedRows,
    handleEditCell,
    handleSave,
    showSuccessIcon,
    individualSaveLoading,
    succesResponse,
    handleDeleteCell,
    handleDeleteRow,
    clickedCells,
    selectedRows,
    feeAmounts,
    focusedCellKey,
    handleRowAllClick,
    handleRowClick,
    isSelected,
    studentsData,
  ]);
  // const feeCollectionListColumns: DataTableColumn<StudentFeeStatusDataType>[] = useMemo(
  //   () => [
  //     {
  //       name: 'slNo',
  //       headerLabel: 'Admission No',
  //       renderCell: (_, index) => {
  //         return (
  //           <Typography variant="subtitle1" fontSize={13}>
  //             100{index + 1}
  //           </Typography>
  //         );
  //       },
  //       sortable: true,
  //     },
  //     {
  //       name: 'studentName',
  //       headerLabel: 'Student Name',
  //       renderCell: (row) => {
  //         return (
  //           <Stack direction="row" alignItems="center" gap={1}>
  //             <Avatar src="" />
  //             <Typography variant="subtitle2" fontSize={13}>
  //               {row.studentName}
  //             </Typography>
  //           </Stack>
  //         );
  //       },
  //     },
  //     {
  //       name: 'admNo',
  //       headerLabel: 'Admission No.',
  //       dataKey: 'admissionNo',
  //     },
  //     {
  //       name: 'classDiv',
  //       dataKey: 'className',
  //       headerLabel: 'Class',
  //     },
  //     {
  //       name: 'prvYearPending',
  //       headerLabel: 'Prev Year Pending',
  //       dataKey: 'prvYearPending',
  //     },
  //     {
  //       name: 'totalFees',
  //       headerLabel: 'Total Fees',
  //       dataKey: 'totalFee',
  //     },
  //     {
  //       name: 'paid',
  //       headerLabel: 'Paid',
  //       renderCell: (row) => {
  //         return (
  //           <Typography color="success" sx={{ color: theme.palette.success.main }} variant="subtitle1">
  //             {row.paid}
  //           </Typography>
  //         );
  //       },
  //     },
  //     {
  //       name: 'balance',
  //       headerLabel: 'Balance',
  //       renderCell: (row) => {
  //         return (
  //           <Typography color="error" variant="subtitle1" fontSize={14}>
  //             {row.balance}
  //           </Typography>
  //         );
  //       },
  //     },
  //   ],
  //   [theme, handleShowPayCollections]
  // );

  // const getRowKey = useCallback(
  //   (row: GetOptionalFeeStudentMapDataType, index: number) => `${row.studentId}_${index}`,
  //   []
  // );

  const getRowKey = useCallback((row: GetIndividualFeeStudentMapDataType, index?: number) => {
    if (index === undefined) {
      return `${row.studentId}_undefined`;
    }
    return `${row.studentId}_${index}`;
  }, []);

  return (
    <Page title="Fees Collection">
      <IndividualFeeSettingRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Stack direction="row" justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Individual Optional Fee Settings
            </Typography>
            <Box pb={1} sx={{ flexShrink: 0 }}>
              {selectedRows.length > 0 && (
                <Tooltip title="Delete">
                  <IconButton
                    sx={{ visibility: { xs: 'hidden', sm: 'visible' } }}
                    aria-label="delete"
                    color="error"
                    onClick={handleDeleteMultiple}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Tooltip>
              )}
              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ mr: { xs: 0, sm: 1 } }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton
                  aria-label="delete"
                  color="primary"
                  sx={{ mr: { xs: 0, sm: 1 } }}
                  onClick={() => setShowFilter((x) => !x)}
                >
                  <IoIosArrowUp />
                </IconButton>
              )}
            </Box>
          </Stack>
          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={4} container spacing={2} alignItems="end">
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter?.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                        disabled={YearStatus === 'loading'}
                        endAdornment={
                          YearStatus === 'loading' && (
                            <CircularProgress sx={{ position: 'relative', right: 15 }} size={24} />
                          )
                        }
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.length !== 0 ? (
                          YearData?.map((opt) => (
                            <MenuItem key={opt.accademicId} value={opt.accademicId}>
                              {opt.accademicTime}
                            </MenuItem>
                          ))
                        ) : (
                          <MenuItem>No Data</MenuItem>
                        )}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Type
                      </Typography>
                      <Select
                        labelId="feeTypeFilter"
                        id="feeTypeFilterSelect"
                        value={feeTypeFilter.toString()}
                        onChange={handleFeeTypeChange}
                        placeholder="Select Year"
                      >
                        <MenuItem sx={{ display: 'none' }} value={0}>
                          Select Type
                        </MenuItem>
                        {FEE_TYPE_ID_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Section
                      </Typography>
                      <Select
                        labelId="classSectionsFilter"
                        id="classSectionsFilterSelect"
                        value={classSectionsFilter?.toString()}
                        onChange={handleClassSectionChange}
                        placeholder="Select Section"
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Section
                        </MenuItem>
                        {ClassSectionsData.map((opt) => (
                          <MenuItem key={opt.sectionId} value={opt.sectionId}>
                            {opt.sectionName}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Class
                      </Typography>
                      <Select
                        disabled={classListData.length === 0}
                        labelId="classFilter"
                        id="classFilter"
                        value={classFilter?.toString()}
                        onChange={handleClassChange}
                        placeholder="Select Class"
                        sx={{
                          '& .MuiInputBase-input.Mui-disabled': {
                            backgroundColor: theme.palette.grey[200],
                          },
                        }}
                        MenuProps={{
                          PaperProps: {
                            style: {
                              maxHeight: '270px', // Adjust the value to your desired height
                            },
                          },
                        }}
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Class
                        </MenuItem>
                        {classListData.map((opt) => (
                          <MenuItem key={opt.classId} value={opt.classId}>
                            {opt.className}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Student
                      </Typography>
                      <StudentsPickerField
                        width="100%"
                        // setSelectedStudentData={setSelectedStudentData}
                        componentsPropsWidth={350}
                        loadRecentPaidList={loadIndividualFeeList}
                        currentRecentPaidListRequest={currentIndividualFeeSettingRequest}
                        setSelectedStudentIds={setSelectedStudentIds}
                        classId={classFilter}
                        academicId={academicYearFilter}
                        // multiple
                      />
                    </FormControl>
                  </Grid>
                  {/* <Grid item lg={2} md={6} sm={6} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Date
                      </Typography>
                      <DatePickers name="messageDateFilter" value="" />
                    </FormControl>
                  </Grid> */}

                  <Grid item lg="auto">
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      {/* <Button type="submit" variant="contained" color="primary" fullWidth>
                        Search
                      </Button> */}
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            {classFilter !== 0 && (
              <Box pr={1} mt={!showFilter ? 2 : 0} display="flex" justifyContent="end">
                <LoadingButton
                  loading={savingAll}
                  onClick={handleAllSave}
                  disabled={saveAllButtonDisabled}
                  color="success"
                  startIcon={<SaveIcon fontSize="small" />}
                  size="small"
                  variant="contained"
                  sx={{
                    //  py: 0.2, px: 1,
                    fontSize: 12,
                  }}
                >
                  {/* {switchToUpdateButton ? 'Update All ' : 'Save All'} */}
                  {savingAll ? 'Saving All' : 'Save All'}
                </LoadingButton>
              </Box>
            )}
            {classFilter !== 0 ? (
              <Paper
                className="card-table-container"
                sx={{
                  marginTop: '12px',
                  border: optionalFeeData.length !== 0 ? `2px solid ${theme.palette.grey[200]} ` : '',
                }}
              >
                <DTVirtuoso
                  // disabledCheckBox={studentsData.map((m) => m.feeMapped?.length === 0)}
                  tableStyles={{ minWidth: { xs: '1100px' } }}
                  // hoverDisable
                  showHorizontalScroll
                  // ShowCheckBox
                  setSelectedRows={setSelectedRows}
                  selectedRows={selectedRows}
                  columns={IndividualFeeSettingListColumns}
                  data={filteredData}
                  getRowKey={getRowKey}
                  // fetchStatus={optionalFeeSettingListStatus}
                  fetchStatus="success"
                  // showEmptyIconState={classFilter}
                />
              </Paper>
            ) : (
              <Box
                display="flex"
                alignItems="center"
                justifyContent="center"
                width="100%"
                height={{ xs: 'calc(100vh - 400px)', sm: 'calc(100vh - 400px)', lg: 'calc(100vh - 400px)' }}
              >
                <Stack direction="column" alignItems="center">
                  <img src={NoData} width="150px" alt="" />
                  <Typography variant="subtitle2" mt={2} color="GrayText">
                    No data found !
                  </Typography>
                </Stack>
              </Box>
            )}
          </div>
        </Card>
      </IndividualFeeSettingRoot>
      <PositionedSnackbar
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        content="Save All Successfully"
        open={snackBar}
        TransitionComponent="SlideTransition"
        onClose={() => setSnackBar(false)}
      />
    </Page>
  );
}
