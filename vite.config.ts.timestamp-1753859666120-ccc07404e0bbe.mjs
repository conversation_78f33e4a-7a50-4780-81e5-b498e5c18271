// vite.config.ts
import { defineConfig } from "file:///E:/passdaily-web/node_modules/.pnpm/vite@4.4.3_@types+node@16.18.38_terser@5.19.0/node_modules/vite/dist/node/index.js";
import react from "file:///E:/passdaily-web/node_modules/.pnpm/@vitejs+plugin-react@3.1.0_vite@4.4.3_@types+node@16.18.38_terser@5.19.0_/node_modules/@vitejs/plugin-react/dist/index.mjs";
import alias from "file:///E:/passdaily-web/node_modules/.pnpm/@rollup+plugin-alias@4.0.4_rollup@2.79.1/node_modules/@rollup/plugin-alias/dist/es/index.js";
import { resolve } from "path";
import { VitePWA } from "file:///E:/passdaily-web/node_modules/.pnpm/vite-plugin-pwa@0.16.7_vite@4.4.3_@types+node@16.18.38_terser@5.19.0__workbox-build@7.0.0_@ty_d5aopbqjqf4r2t5kpv7qg2ni7u/node_modules/vite-plugin-pwa/dist/index.js";
var __vite_injected_original_dirname = "E:\\passdaily-web";
var projectRootDir = resolve(__vite_injected_original_dirname);
var vite_config_default = defineConfig({
  plugins: [
    // mkcert({ keyFileName: httpsOptions.key, certFileName: httpsOptions.cert }),
    react(),
    VitePWA({
      manifest: {
        name: "passdaily",
        short_name: "Passdaily",
        // description: '',
        icons: [
          {
            src: "/logo-small.svg",
            sizes: "192x192",
            type: "image/png",
            purpose: "any"
          },
          {
            src: "/logo-small.svg",
            sizes: "512x512",
            type: "image/png",
            purpose: "maskable"
          }
        ]
      }
    }),
    alias({
      entries: [
        {
          find: "@",
          replacement: resolve(projectRootDir, "src")
        }
      ]
    })
    // checker({
    //   typescript: true,
    // }),
  ],
  server: {
    open: true,
    port: 8080
    // https: true,
    // hmr: {
    //   host: 'localhost',
    //   port: 8081,
    //   // protocol: 'wss',
    // },
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
