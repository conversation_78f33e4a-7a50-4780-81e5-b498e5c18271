import Loadable from '@/components/shared/Loadable';
import { ReceiptPDF } from '@/components/shared/ReceiptPDF';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';

const ManageFee = Loadable(lazy(() => import('@/pages/ManageFee')));
const Overview = Loadable(lazy(() => import('@/features/ManageFee/Fee/Overview')));
const BasicFeeList = Loadable(lazy(() => import('@/features/ManageFee/FeesList/BasicFeeList')));
const TermFeeList = Loadable(lazy(() => import('@/features/ManageFee/TermFeeList/TermFeeList')));
const FeeCollection = Loadable(lazy(() => import('@/features/ManageFee/FeeCollection/FeeCollection')));
const Scholarship = Loadable(lazy(() => import('@/features/ManageFee/Scholarship/Scholarship')));
const PayFee = Loadable(lazy(() => import('@/features/ManageFee/PayFee/PayFee')));
const PayFeeDetails = Loadable(lazy(() => import('@/features/ManageFee/FeesDetails/FeesDetails')));
const TotalFeePaidList = Loadable(lazy(() => import('@/features/ManageFee/PaidList/TotalFeePaidList')));
const BasicFeePaidList = Loadable(lazy(() => import('@/features/ManageFee/PaidList/BasicFeePaidList')));
const TermFeePaidList = Loadable(lazy(() => import('@/features/ManageFee/PaidList/TermFeePaidList')));
const DailyReport = Loadable(lazy(() => import('@/features/ManageFee/Unused/DailyReport')));
const TotalFeePendingList = Loadable(lazy(() => import('@/features/ManageFee/PendingList/TotalFeePendingList')));
const BasicFeePendingList = Loadable(lazy(() => import('@/features/ManageFee/PendingList/BasicFeePendingList')));
const TermFeePendingList = Loadable(lazy(() => import('@/features/ManageFee/PendingList/TermFeePendingList')));
const BasicFeeSetting = Loadable(lazy(() => import('@/features/ManageFee/BasicFeeSetting/BasicFeeSetting')));
const FineSetting = Loadable(lazy(() => import('@/features/ManageFee/Fine/Fine')));
const OptionalFee = Loadable(lazy(() => import('@/features/ManageFee/OptionalFee/OptionalFee')));
const FeesDetails = Loadable(lazy(() => import('@/features/ManageFee/FeesDetails/FeesDetails')));
const FeeDateSettings = Loadable(lazy(() => import('@/features/ManageFee/FeeDateSetting/FeeDateSettings')));
const OptionalFeeSetting = Loadable(lazy(() => import('@/features/ManageFee/OptionalFee/OptionalFeeSetting')));
const ScholarshipSetting = Loadable(lazy(() => import('@/features/ManageFee/Unused/ScholarshipSetting')));
const FeeReport = Loadable(lazy(() => import('@/features/ManageFee/FeeDateSetting/FeeDateSettings')));
const FineSetting2 = Loadable(lazy(() => import('@/features/ManageFee/Unused/FineSetting2')));
const FineSetting3 = Loadable(lazy(() => import('@/features/ManageFee/Unused/FineSetting3')));
const OptionalFeeReport = Loadable(lazy(() => import('@/features/ManageFee/Unused/OptionalFeeReport')));
const OptionalFeeSetting2 = Loadable(lazy(() => import('@/features/ManageFee/OptionalFeeSetting2')));
const IndividualFeeSetting = Loadable(lazy(() => import('@/features/ManageFee/IndividualFeeSetting')));
const FeeListGroup = Loadable(lazy(() => import('@/features/ManageFee/Groups/FeeList')));
const FeePaidListGroup = Loadable(lazy(() => import('@/features/ManageFee/Groups/FeePaidLists')));
const FeePendingListGroup = Loadable(lazy(() => import('@/features/ManageFee/Groups/FeePendingLists')));
const FeeSettingsGroup = Loadable(lazy(() => import('@/features/ManageFee/Groups/FeeSettings')));
const RND = Loadable(lazy(() => import('@/components/shared/RND/RND')));

export const manageFeeRoutes: RouteObject[] = [
  {
    path: 'manage-fee/paid-receipt',
    element: <ReceiptPDF />,
  },
  {
    path: '/manage-fee',
    element: <ManageFee />,
    children: [
      {
        path: 'overview',
        element: <Overview />,
      },
      // {
      //   path: 'basic-fee-list',
      //   element: <BasicFeeList />,
      // },
      // {
      //   path: 'term-fee-list',
      //   element: <TermFeeList />,
      // },
      {
        path: 'collection',
        element: <FeeCollection />,
      },
      {
        path: 'pay-fee',
        element: <PayFee />,
      },
      {
        path: 'pay-fee-details',
        element: <PayFeeDetails />,
      },
      // {
      //   path: 'total-paid-list',
      //   element: <TotalFeePaidList />,
      // },
      // {
      //   path: 'basicfee-paid-list',
      //   element: <BasicFeePaidList />,
      // },
      // {
      //   path: 'termfee-paid-list',
      //   element: <TermFeePaidList />,
      // },
      {
        path: 'daily-report',
        element: <DailyReport />,
      },
      // {
      //   path: 'total-pending-list',
      //   element: <TotalFeePendingList />,
      // },
      // {
      //   path: 'total-pending-basic-list',
      //   element: <BasicFeePendingList />,
      // },
      // {
      //   path: 'total-pending-term-list',
      //   element: <TermFeePendingList />,
      // },
      // {
      //   path: 'setting',
      //   element: <FeeSetting />,
      // },
      // {
      //   path: 'fine-setting',
      //   element: <FineSetting />,
      // },
      // {
      //   path: 'optional',
      //   element: <OptionalFee />,
      // },
      // {
      //   path: 'fee-date-settings',
      //   element: <FeeDateSettings />,
      // },
      // {
      //   path: 'optional-fee-settings',
      //   element: <OptionalFeeSetting />,
      // },
      // {
      //   path: 'scholarship-settings',
      //   element: <ScholarshipSetting />,
      // },
      {
        path: 'fees-details',
        element: <FeesDetails />,
      },
      {
        path: 'fee-report',
        element: <FeeReport />,
      },
      {
        path: 'fine-settings2',
        element: <FineSetting2 />,
      },
      {
        path: 'fine-settings3',
        element: <FineSetting3 />,
      },
      {
        path: 'optional-fee-report',
        element: <OptionalFeeReport />,
      },
      {
        path: 'scholarship',
        element: <Scholarship />,
      },
      {
        path: 'bus-stop-settings',
        element: <OptionalFeeSetting2 />,
      },
      {
        path: 'individual-fee-settings',
        element: <IndividualFeeSetting />,
      },
      {
        path: 'fee-lists',
        element: <FeeListGroup />,
        children: [
          {
            path: 'basic',
            element: <BasicFeeList />,
          },
          {
            path: 'term',
            element: <TermFeeList />,
          },
        ],
      },
      {
        path: 'fee-paid-lists',
        element: <FeePaidListGroup />,
        children: [
          {
            path: 'total',
            element: <TotalFeePaidList />,
          },
          {
            path: 'fee',
            element: <BasicFeePaidList />,
          },
          {
            path: 'term',
            element: <TermFeePaidList />,
          },
        ],
      },
      {
        path: 'fee-pending-lists',
        element: <FeePendingListGroup />,
        children: [
          {
            path: 'total',
            element: <TotalFeePendingList />,
          },
          {
            path: 'fee',
            element: <BasicFeePendingList />,
          },
          {
            path: 'term',
            element: <TermFeePendingList />,
          },
        ],
      },
      {
        path: 'fee-settings',
        element: <FeeSettingsGroup />,
        children: [
          {
            path: 'fee-amount',
            element: <FeeSettingsGroup />,
          },
          {
            path: 'term-fee',
            element: <FeeDateSettings />,
          },
          {
            path: 'scholarship',
            element: <Scholarship />,
          },
          {
            path: 'fine',
            element: <FineSetting />,
          },
          {
            path: 'optional-fee',
            element: <OptionalFeeSetting />,
          },
          {
            path: 'individual-fee',
            element: <IndividualFeeSetting />,
          },
        ],
      },
      // {
      //   path: 'rnd',
      //   element: <RND />,
      // },
    ],
  },
];
