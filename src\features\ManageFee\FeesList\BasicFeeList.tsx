/* eslint-disable no-else-return */
/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/alt-text */
import React, { FormEvent, useCallback, useEffect, useMemo, useState } from 'react';
import Page from '@/components/shared/Page';
import {
  Box,
  Divider,
  Grid,
  Paper,
  Stack,
  TextField,
  Button,
  Typography,
  Card,
  IconButton,
  Collapse,
  Tooltip,
  FormControl,
  Select,
  MenuItem,
  Chip,
  SelectChangeEvent,
  FormControlLabel,
  RadioGroup,
  Radio,
} from '@mui/material';
import styled from 'styled-components';
import SearchIcon from '@mui/icons-material/Search';
import { IoIosArrowUp } from 'react-icons/io';
import ErrorIcon from '@/assets/ManageFee/ErrorIcon.json';
import Success from '@/assets/ManageFee/Success.json';
import deleteBin from '@/assets/ManageFee/deleteBin.json';
import deleteSuccess from '@/assets/ManageFee/deleteSuccess.json';
import errorIcon from '@/assets/ManageFee/Error.json';
import ModeEditIcon from '@mui/icons-material/ModeEdit';
import DeleteIcon from '@mui/icons-material/Delete';
import { DeleteMessage } from '@/components/shared/Popup/DeleteMessage';
import NoData from '@/assets/no-datas.png';
import TemporaryDrawer from '@/components/shared/Popup/Drawer';
import { MdAdd } from 'react-icons/md';
import useAuth from '@/hooks/useAuth';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { useConfirm } from '@/components/shared/Popup/Confirmation';
import { useAppSelector } from '@/hooks/useAppSelector';
import {
  getBasicFeeListData,
  getBasicFeeListStatus,
  getManageFeeSubmitting,
  getYearData,
  getYearStatus,
} from '@/config/storeSelectors';
import {
  CreateBasicFeeSettingTitleDataType,
  DeleteBasicFeeListType,
  GetBasicFeeListRequestType,
} from '@/types/ManageFee';
import { createBasicFeeSettingTitle, deleteBasicFeeList, fetchBasicFeeList } from '@/store/ManageFee/manageFee.thunks';
import SaveIcon from '@mui/icons-material/Save';

import DataTable, { DataTableColumn } from '@/components/shared/TableComponents/DataTable';
import { SuccessMessage } from '@/components/shared/Popup/SuccessMessage';
import { ErrorMessage } from '@/components/shared/Popup/ErrorMessage';
import { fetchYearList } from '@/store/Dashboard/dashboard.thunks';
import dayjs, { Dayjs } from 'dayjs';
import DatePickers from '@/components/shared/Selections/DatePicker';
import LoadingButton from '@mui/lab/LoadingButton';
import { FEE_TYPE_ID_OPTIONS, FEE_TYPE_OPTIONS } from '@/config/Selection';
import { CreateEditBasicFeeTitleForm } from './CreateEditBasicFeeTitleForm';
import { useTheme } from '@mui/material';
import useSettings from '@/hooks/useSettings';
import DTVirtuoso from '@/components/shared/RND/DataTableVirtuoso';
import { Checkbox } from '@mui/material';
import CreateMultipleTitleForm from './CreateMultipleTitleForm';

const ListRoot = styled.div`
  padding: 1rem;
  @media screen and (max-width: 576px) {
    padding: 0.5rem;
  }
  .Card {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 160px);
    @media screen and (max-width: 576px) {
      height: 100%;
    }

    .card-main-body {
      display: flex;
      flex-direction: column;
      max-height: calc(100% - 50px);
      flex-grow: 1;

      .card-table-container {
        flex-grow: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        border: 1px solid ${(props) => props.theme.palette.grey[200]};
        overflow: hidden;

        .MuiTableContainer-root {
          height: 100%;
        }

        .MuiTablePagination-root {
          flex-grow: 1;
          flex-shrink: 0;
        }
        .MuiTableCell-root:first-child {
          padding: 5px;
        }
      }
    }
  }
`;

const currentDate = new Date();

const DefaultBasicFeeInfo: CreateBasicFeeSettingTitleDataType = {
  adminId: 0,
  feeId: 0,
  feeTitle: '',
  feeType: 0,
  startDate: '',
  endDate: '',
  feeTypeId: 0,
};

function BasicFeeList() {
  const { user } = useAuth();
  const dispatch = useAppDispatch();
  const { confirm } = useConfirm();
  const adminId: number = user ? user.accountId : 0;
  const theme = useTheme();
  const { themeMode } = useSettings();
  const isLight = themeMode === 'light';
  const [showFilter, setShowFilter] = useState(true);
  const [drawerOpen, setDrawerOpen] = React.useState<boolean>(false);
  const [selectedBasicFeeDetail, setSelectedBasicFeeDetail] =
    useState<CreateBasicFeeSettingTitleDataType>(DefaultBasicFeeInfo);
  const [basicTitleFilter, setBasicTitleFilter] = useState('');
  const [basicFeeData, setBasicFeeData] = useState<CreateBasicFeeSettingTitleDataType[]>([]);
  // const [statusFilter, setStatusFilter] = useState(-1);
  const YearData = useAppSelector(getYearData);
  const YearStatus = useAppSelector(getYearStatus);
  // const initialAcademicId = YearData.length > 0 ? YearData[0].accademicId : '';
  const defaultYear = YearData[0]?.accademicId || 0;
  const [academicYearFilter, setAcademicYearFilter] = useState(defaultYear);
  const [feeTypeFilter, setFeeTypeFilter] = useState(0);
  const basicFeeListData = useAppSelector(getBasicFeeListData);
  const basicFeeListStatus = useAppSelector(getBasicFeeListStatus);
  const isSubmitting = useAppSelector(getManageFeeSubmitting);
  const [selectedRows, setSelectedRows] = useState<CreateBasicFeeSettingTitleDataType[]>([]);
  const [editedRows, setEditedRows] = useState<CreateBasicFeeSettingTitleDataType[]>([]);
  const [currentPage, setCurrentPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [showCreateMultiple, setShowCreateMultiple] = React.useState<'list' | 'multiple'>('list');

  const filteredItems = basicFeeData.filter((item) =>
    item.feeTitle.toLowerCase().includes(basicTitleFilter.toLowerCase())
  );

  const currentBasicFeeListRequest = useMemo(
    () => ({
      adminId,
      accademicId: academicYearFilter,
      feeTitle: basicTitleFilter,
      feeTypeId: feeTypeFilter,
    }),
    [basicTitleFilter, academicYearFilter, adminId, feeTypeFilter]
  );
  const loadBasicFeeList = useCallback(
    async (request: GetBasicFeeListRequestType) => {
      try {
        const response = await dispatch(fetchBasicFeeList(request));
        // Handle the response if needed
        console.log('response::::----', response);
      } catch (error) {
        // Log the error or handle it appropriately
        console.error('Failed to load basic fee list:', error);
      }
    },
    [dispatch]
  );
  // const req = { adminId: 8, accademicId: academicYearFilter, feeTypeId: feeTypeFilter, feeTitle: '' };

  // const fetchFees = useCallback(async () => {
  //   try {
  //     const response = await axios.post('http://api.passdaily.in/api/termfee/basicfeelist', currentBasicFeeListRequest);
  //     setBasicFeeData(response.data);
  //     console.log('responseeee::::----', response.data);
  //   } catch (err) {
  //     console.log('Error fetching fees:', err);
  //   } finally {
  //     console.log('Fetch completed');
  //   }
  // }, [currentBasicFeeListRequest]);

  // useEffect(() => {
  //   fetchFees();
  // }, [fetchFees]);

  useEffect(() => {
    // if (YearStatus === 'idle') {
    //   setAcademicYearFilter(YearData[0]?.accademicId);
    // }
    if (basicFeeListStatus === 'idle') {
      loadBasicFeeList(currentBasicFeeListRequest);
      dispatch(fetchYearList(adminId));
    }
    setBasicFeeData(basicFeeListData);
    // console.log('datass::', cuseEffectlassListData);
    console.log('selectedRows::', selectedRows);
  }, [
    loadBasicFeeList,
    basicFeeListStatus,
    selectedRows,
    currentBasicFeeListRequest,
    adminId,
    dispatch,
    basicFeeListData,
    YearData,
    YearStatus,
  ]);

  // const handleStatusChange = (e: SelectChangeEvent) => {
  //   const statusVal = parseInt(e.target.value, 10);
  //   setStatusFilter(statusVal);
  //   loadBasicFeeList({
  //     ...currentBasicFeeListRequest,
  //     classStatus: statusVal,
  //   });
  // };

  const handleYearChange = (e: SelectChangeEvent) => {
    setAcademicYearFilter(parseInt(YearData.filter((item) => item.accademicId === e.target.value)[0].accademicId, 10));
    loadBasicFeeList({ ...currentBasicFeeListRequest, accademicId: parseInt(e.target.value, 10) });
  };
  const handleFeeTypeChange = (e: SelectChangeEvent) => {
    const feeTypeId = parseInt(e.target.value, 10);
    setFeeTypeFilter(feeTypeId);
    loadBasicFeeList({ ...currentBasicFeeListRequest, feeTypeId: parseInt(e.target.value, 10) });
  };

  // const handleYearChange = (e: SelectChangeEvent) => {
  //   const selectedAcademicId = parseInt(e.target.value, 10);
  //   setAcademicYearFilter(selectedAcademicId);
  //   loadBasicFeeList({ ...currentBasicFeeListRequest, accademicId: selectedAcademicId });
  // };

  const handleReset = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      setAcademicYearFilter(defaultYear);
      setFeeTypeFilter(0);
      setBasicTitleFilter('');
      loadBasicFeeList({
        adminId,
        accademicId: academicYearFilter,
        feeTitle: basicTitleFilter,
        feeTypeId: feeTypeFilter,
      });
    },
    [loadBasicFeeList, adminId, defaultYear, academicYearFilter, basicTitleFilter, feeTypeFilter]
  );

  const toggleDrawerOpen = () => {
    setDrawerOpen(true);
  };

  const toggleDrawerClose = () => setDrawerOpen(false);

  const handleSaveOrEdit = useCallback(
    async (values: CreateBasicFeeSettingTitleDataType, mode: 'create' | 'edit') => {
      try {
        console.log('values::::----', values);
        if (mode === 'create') {
          console.log('values::::----', values);
          const response = await dispatch(createBasicFeeSettingTitle(values)).unwrap();
          console.log('response::::----', response);
          if (response.id > 0) {
            toggleDrawerClose();
            loadBasicFeeList({ ...currentBasicFeeListRequest });
            const successMessage = (
              <SuccessMessage icon="" loop={false} jsonIcon={Success} message="Fee title create successfully" />
            );
            await confirm(successMessage, '', { okLabel: 'Ok', showOnlyOk: true });
          } else if (response.id === 0) {
            toggleDrawerClose();
            const errorMessage = <ErrorMessage icon="" jsonIcon={ErrorIcon} message="Fee title already created" />;
            await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
            toggleDrawerOpen();
          } else {
            const errorMessage = <ErrorMessage icon="" jsonIcon={ErrorIcon} message="Fee title create failed" />;
            await confirm(errorMessage, '', { okLabel: 'Ok', showOnlyOk: true });
          }
        } else {
          const { startDate, endDate } = values;

          const isDateFormatted = (date: string) => {
            const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;
            return dateRegex.test(date);
          };

          // Function to format dates
          const formatDate = (date: Dayjs | string | number | null): string | null => {
            if (date === null) return null;
            if (dayjs.isDayjs(date)) return date.format('DD/MM/YYYY');
            if (typeof date === 'string' && isDateFormatted(date)) return date; // Already formatted
            if (typeof date === 'string' || typeof date === 'number') return dayjs(date).format('DD/MM/YYYY');
            return null; // Fallback for any unexpected types
          };

          // Convert startDate and endDate to DD/MM/YYYY format
          const formattedStartDate = formatDate(startDate);
          const formattedEndDate = formatDate(endDate);
          const updateReq = {
            ...values,
            startDate: formattedStartDate,
            endDate: formattedEndDate,
            dbResult: 'string',
            updatedId: 0,
          };
          console.log('updateReq::::----', updateReq);
          const response = await dispatch(createBasicFeeSettingTitle(updateReq)).unwrap();
          console.log('values::::----', values);

          if (response) {
            setDrawerOpen(false);
            const successMessage = (
              <SuccessMessage jsonIcon={Success} loop={false} message="Fee Title updated successfully" />
            );
            await confirm(successMessage, 'Fee Title Updated', { okLabel: 'Ok', showOnlyOk: true });

            loadBasicFeeList({ ...currentBasicFeeListRequest });
          } else {
            const errorMessage = <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="Fee Title updated failed" />;
            await confirm(errorMessage, 'Fee Title Update', { okLabel: 'Ok', showOnlyOk: true });
          }
        }
      } catch {
        const errorMessage = (
          <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="Something went wrong please try again " />
        );
        await confirm(errorMessage, 'Fee Title Update', { okLabel: 'Ok', showOnlyOk: true });
      }
    },
    [dispatch, confirm, loadBasicFeeList, currentBasicFeeListRequest]
  );

  const handleUpdateAll = useCallback(async () => {
    try {
      const promises = editedRows.map(async (row) => {
        const { startDate, endDate } = row;
        console.log('editedRows::::----', editedRows);
        // Convert startDate and endDate to DD/MM/YYYY format
        // Function to format dates if they are not already in DD/MM/YYYY format
        // Function to check if date is in DD/MM/YYYY format
        const isDateFormatted = (date: string) => {
          const dateRegex = /^\d{2}\/\d{2}\/\d{4}$/;
          return dateRegex.test(date);
        };

        // Function to format dates
        const formatDate = (date: Dayjs | string | number | null): string | null => {
          if (date === null) return null;
          if (dayjs.isDayjs(date)) return date.format('DD/MM/YYYY');
          if (typeof date === 'string' && isDateFormatted(date)) return date; // Already formatted
          if (typeof date === 'string' || typeof date === 'number') return dayjs(date).format('DD/MM/YYYY');
          return null; // Fallback for any unexpected types
        };

        // Convert startDate and endDate to DD/MM/YYYY format
        const formattedStartDate = formatDate(startDate);
        const formattedEndDate = formatDate(endDate);
        const updateReq = {
          ...row,
          startDate: formattedStartDate,
          endDate: formattedEndDate,
          dbResult: 'string',
          updatedId: 0,
        };
        console.log('updateReq::::----', updateReq);
        console.log('startDate::::----', startDate);
        const response = await dispatch(createBasicFeeSettingTitle(updateReq)).unwrap();
        return response;
      });

      const responses = await Promise.all(promises);

      // Check if all updates were successful
      const isSuccess = responses.every((response) => response);

      if (isSuccess) {
        setSelectedRows([]);
        loadBasicFeeList({ ...currentBasicFeeListRequest });
        const successMessage = (
          <SuccessMessage loop={false} jsonIcon={Success} message="Fee Title updated successfully" />
        );
        await confirm(successMessage, 'Fee Titles Updated', { okLabel: 'Ok', showOnlyOk: true });
      } else {
        const errorMessage = <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="Fee Title updated failed" />;
        await confirm(errorMessage, 'Fee Titles Update', { okLabel: 'Ok', showOnlyOk: true });
      }
    } catch (error) {
      const errorMessage = (
        <ErrorMessage loop={false} jsonIcon={ErrorIcon} message="Something went wrong please try again " />
      );
      await confirm(errorMessage, 'Fee Title Update', { okLabel: 'Ok', showOnlyOk: true });
      // Handle error
      console.error('Error updating fee title:', error);
    }
  }, [dispatch, confirm, loadBasicFeeList, currentBasicFeeListRequest, editedRows]);

  const handleAddTermFeeList = () => {
    setSelectedBasicFeeDetail(DefaultBasicFeeInfo);
    setDrawerOpen(true);
  };

  const handleEditBasicFeeList = useCallback((feeObj: CreateBasicFeeSettingTitleDataType) => {
    setSelectedBasicFeeDetail(feeObj);
    setDrawerOpen(true);
  }, []);

  const handleDeleteTermFeeList = useCallback(
    async (feeObj: CreateBasicFeeSettingTitleDataType) => {
      const { feeId } = feeObj;
      const deleteConfirmMessage = (
        <DeleteMessage
          jsonIcon={deleteBin}
          message={
            <div>
              Are you sure you want to delete the Term Fee Row <br />
              &quot;{feeObj.feeTitle}&quot; ?
            </div>
          }
        />
      );
      if (await confirm(deleteConfirmMessage, 'Delete Row?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
        const sendReq = [{ adminId, accademicId: academicYearFilter, feeId, dbResult: 'string', deletedId: 0 }];

        console.log('sendReq', sendReq);

        const deleteResponse = await dispatch(deleteBasicFeeList(sendReq));
        console.log('deleteResponse::::----', deleteResponse);
        if (deleteResponse && Array.isArray(deleteResponse.payload)) {
          const results: DeleteBasicFeeListType[] = deleteResponse.payload;
          const errorMessages = results.find((result) => result.dbResult === 'Failed');
          const successMessages = results.find((result) => result.dbResult === 'Success');
          // Reload only the specific message template with the deleted messageId

          if (!errorMessages) {
            const deleteSuccessMessage = (
              <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete successfully" />
            );
            await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
          } else if (!successMessages) {
            const deleteErrorMessage = <DeleteMessage jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          } else {
            const deleteErrorMessage = <DeleteMessage jsonIcon={errorIcon} message="Delete failed" />;
            await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
          }
          // loadBasicFeeList({ ...currentBasicFeeListRequest });

          // setSnackBar(true);
          setBasicFeeData((prevDetails) => prevDetails.filter((item) => item.feeId !== feeId));
        }
      }
    },
    [dispatch, academicYearFilter, adminId, confirm]
  );

  const handleDeleteMultiple = useCallback(async () => {
    const sendConfirmMessage = (
      <DeleteMessage jsonIcon={deleteBin} message={<div>Are you sure you want to delete all ?</div>} />
    );
    if (await confirm(sendConfirmMessage, 'Delete Basic Fee?', { okLabel: 'Delete', cancelLabel: 'Cancel' })) {
      // const deleteResponse = await dispatch(deleteBasicFeeList(sendReq));
      const sendRequests: DeleteBasicFeeListType[] = await Promise.all(
        selectedRows?.map(async (row) => {
          const sendReq = {
            adminId,
            accademicId: academicYearFilter,
            feeId: row.feeId,
            dbResult: '',
            deletedId: 0,
          };
          return sendReq;
        }) || []
      );
      const deleteResponse = await dispatch(deleteBasicFeeList(sendRequests));

      console.log('deleteResponse', deleteResponse);
      if (deleteResponse && Array.isArray(deleteResponse.payload)) {
        const results: DeleteBasicFeeListType[] = deleteResponse.payload;
        const errorMessages = results.find((result) => result.dbResult === 'Failed');
        const successMessages = results.find((result) => result.dbResult === 'Success');

        if (!errorMessages) {
          loadBasicFeeList({ ...currentBasicFeeListRequest });
          const deleteSuccessMessage = (
            <SuccessMessage loop={false} jsonIcon={deleteSuccess} message="Delete All Successfully" />
          );
          await confirm(deleteSuccessMessage, 'Success', { okLabel: 'Ok', showOnlyOk: true });
        } else if (!successMessages) {
          const deleteErrorMessage = <DeleteMessage loop={false} jsonIcon={errorIcon} message="Delete Failed" />;
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        } else {
          const deleteErrorMessage = (
            <DeleteMessage loop={false} jsonIcon={errorIcon} message="Something went wrong please try again" />
          );
          await confirm(deleteErrorMessage, 'Error', { okLabel: 'Ok', showOnlyOk: true });
        }
        setSelectedRows([]);
        setBasicFeeData((prevDetails) =>
          prevDetails.filter((item: CreateBasicFeeSettingTitleDataType) => !selectedRows.includes(item.feeId))
        );
      }
    }
  }, [confirm, dispatch, selectedRows, adminId, academicYearFilter, loadBasicFeeList, currentBasicFeeListRequest]);

  // Pagination handlers
  const handlePageChange = useCallback(
    (event: any, newPage: any) => {
      setCurrentPage(newPage); // Update current page
    },
    [setCurrentPage]
  );

  const handleChangeRowsPerPage = useCallback(
    (event: any) => {
      setRowsPerPage(parseInt(event.target.value, 10)); // Update rows per page
      // Reset to first page when changing rows per page
    },
    [setRowsPerPage]
  );

  const paginatedData = filteredItems.slice(currentPage * rowsPerPage, currentPage * rowsPerPage + rowsPerPage);

  const pageProps = useMemo(
    () => ({
      rowsPerPageOptions: [10, 20, 30, 50, 100, 200, 300, 500],
      pageNumber: currentPage,
      pageSize: rowsPerPage,
      totalRecords: filteredItems.length,
      onPageChange: handlePageChange,
      onRowsPerPageChange: handleChangeRowsPerPage,
    }),
    [handleChangeRowsPerPage, handlePageChange, currentPage, rowsPerPage, filteredItems]
  );

  const basicFeeListColumns: DataTableColumn<CreateBasicFeeSettingTitleDataType>[] = useMemo(
    () => [
      {
        name: 'feeId',
        headerLabel: 'Sl No',
        renderCell: (row) => {
          return (
            <Typography minWidth={50} fontSize={13} variant="subtitle1">
              {row.feeId}
            </Typography>
          );
        },
        sortable: true,
      },
      {
        name: 'feeTitle',
        headerLabel: 'Fee Title',
        renderCell: (row) => {
          const isSelected = selectedRows.some((selectedRow) => selectedRow.feeId === row.feeId);
          return !isSelected ? (
            <Typography minWidth={200} fontSize={13} variant="subtitle1">
              {row.feeTitle}
            </Typography>
          ) : (
            <TextField
              defaultValue={row.feeTitle}
              inputProps={{
                style: {
                  // padding: '4px 10px',
                  fontSize: '13px',
                  // width: 110,
                },
              }}
              onChange={(e) => {
                const updatedRows = selectedRows.map((item) => {
                  if (item.feeId === row.feeId) {
                    return { ...item, feeTitle: e.target.value };
                  }
                  return item;
                });
                setEditedRows((prev) => {
                  const exists = prev.find((item) => item.feeId === row.feeId);
                  if (exists) {
                    // Update the existing item
                    return prev.map((item) =>
                      item.feeId === row.feeId ? { ...item, feeTitle: e.target.value } : item
                    );
                  } else {
                    // Add new edited item
                    return [...prev, { ...row, feeTitle: e.target.value }];
                  }
                });
              }}
            />
          );
        },
      },
      {
        name: 'feeType',
        headerLabel: 'Fee Type',
        sortable: true,
        renderCell: (row) => {
          const isSelected = selectedRows.some((selectedRow) => selectedRow.feeId === row.feeId);
          return !isSelected ? (
            <Typography minWidth={150} variant="subtitle1" fontSize={13}>
              {row.feeType === 1
                ? 'Standard'
                : row.feeType === 2
                ? 'School'
                : row.feeType === 3
                ? 'Optional'
                : row.feeType === 4
                ? 'Individual'
                : ''}
            </Typography>
          ) : (
            <Select
              name="feeType"
              defaultValue={row.feeType}
              onChange={(e) => {
                const updatedRows = selectedRows.map((item) => {
                  if (item.feeId === row.feeId) {
                    return { ...item, feeType: e.target.value };
                  }
                  return item;
                });
                setEditedRows((prev) => {
                  const exists = prev.find((item) => item.feeId === row.feeId);
                  if (exists) {
                    // Update the existing item
                    return prev.map((item) => (item.feeId === row.feeId ? { ...item, feeType: e.target.value } : item));
                  } else {
                    // Add new edited item
                    return [...prev, { ...row, feeType: e.target.value }];
                  }
                });
              }}
              sx={{ width: 125 }}
            >
              <MenuItem sx={{ display: 'none' }} value={0}>
                Select type
              </MenuItem>
              {FEE_TYPE_OPTIONS.map((opt) => (
                <MenuItem key={opt.id} value={opt.id}>
                  {opt.name}
                </MenuItem>
              ))}
            </Select>
          );
        },
      },
      {
        name: 'startDate',
        headerLabel: 'Start Date',
        renderCell: (row) => {
          const isSelected = selectedRows.some((selectedRow) => selectedRow.feeId === row.feeId);
          const formattedDate = dayjs(row.startDate, 'YYYY/MM/DD').format('DD/MM/YYYY');
          const startDateValue = dayjs(row.startDate, 'YYYY/MM/DD');
          // console.log('startDateValue::::----', startDateValue);
          return !isSelected ? (
            <Typography minWidth={150} fontSize={13} variant="subtitle1">
              {formattedDate}
            </Typography>
          ) : (
            <DatePickers
              width="160px"
              name="startDate"
              value={startDateValue}
              onChange={(date) => {
                if (date) {
                  const updatedRows = selectedRows.map((item) => {
                    if (item.feeId === row.feeId) {
                      return { ...item, startDate: date.format('DD/MM/YYYY') };
                    }
                    return item;
                  });

                  setEditedRows((prev) => {
                    const exists = prev.find((item) => item.feeId === row.feeId);
                    if (exists) {
                      // Update the existing item
                      return prev.map((item) =>
                        item.feeId === row.feeId ? { ...item, startDate: date.format('DD/MM/YYYY') } : item
                      );
                    } else {
                      // Add new edited item
                      return [...prev, { ...row, startDate: date.format('DD/MM/YYYY') }];
                    }
                  });
                }
              }}
            />
          );
        },
      },
      {
        name: 'endDate',
        headerLabel: 'End Date',
        renderCell: (row) => {
          const isSelected = selectedRows.some((selectedRow) => selectedRow.feeId === row.feeId);
          const formattedDate = dayjs(row.endDate, 'YYYY/MM/DD').format('DD/MM/YYYY');
          const endDateValue = dayjs(row.endDate, 'YYYY/MM/DD');
          return !isSelected ? (
            <Typography minWidth={150} fontSize={13} variant="subtitle1">
              {formattedDate}
            </Typography>
          ) : (
            <DatePickers
              width="160px"
              name="endDate"
              value={endDateValue}
              onChange={(date) => {
                if (date) {
                  const updatedRows = selectedRows.map((item) => {
                    if (item.feeId === row.feeId) {
                      return { ...item, endDate: date.format('DD/MM/YYYY') };
                    }
                    return item;
                  });

                  setEditedRows((prev) => {
                    const exists = prev.find((item) => item.feeId === row.feeId);
                    if (exists) {
                      // Update the existing item
                      return prev.map((item) =>
                        item.feeId === row.feeId ? { ...item, endDate: date.format('DD/MM/YYYY') } : item
                      );
                    } else {
                      // Add new edited item
                      return [...prev, { ...row, endDate: date.format('DD/MM/YYYY') }];
                    }
                  });
                }
              }}
            />
          );
        },
      },
      {
        name: 'feeTypeId',
        headerLabel: 'Type',
        sortable: true,
        renderCell: (row) => {
          const isSelected = selectedRows.some((selectedRow) => selectedRow.feeId === row.feeId);
          return !isSelected ? (
            <Typography minWidth={150} variant="subtitle1" fontSize={13}>
              {row.feeTypeId === 1 ? 'Accademic Fee' : row.feeTypeId === 2 ? 'Bus Fee' : ''}
            </Typography>
          ) : (
            <Select
              name="feeTypeId"
              defaultValue={row.feeTypeId}
              onChange={(e) => {
                const updatedRows = selectedRows.map((item) => {
                  if (item.feeId === row.feeId) {
                    return { ...item, feeTypeId: e.target.value };
                  }
                  return item;
                });

                setEditedRows((prev) => {
                  const exists = prev.find((item) => item.feeId === row.feeId);
                  if (exists) {
                    // Update the existing item
                    return prev.map((item) =>
                      item.feeId === row.feeId ? { ...item, feeTypeId: e.target.value } : item
                    );
                  } else {
                    // Add new edited item
                    return [...prev, { ...row, feeTypeId: e.target.value }];
                  }
                });
              }}
              sx={{ width: 125 }}
            >
              <MenuItem sx={{ display: 'none' }} value={0}>
                Select type
              </MenuItem>
              {FEE_TYPE_ID_OPTIONS.map((opt) => (
                <MenuItem key={opt.id} value={opt.id}>
                  {opt.name}
                </MenuItem>
              ))}
            </Select>
          );
        },
      },
      {
        name: 'status',
        headerLabel: 'Status',
        sortable: true,
        renderCell: (row) => {
          const isSelected = selectedRows.some((selectedRow) => selectedRow.feeId === row.feeId);
          return !isSelected ? (
            <Chip
              size="small"
              label={row.status === 1 ? 'Active' : 'Inactive'}
              variant="filled"
              color={row.status === 1 ? 'success' : 'error'}
            />
          ) : (
            <RadioGroup
              row
              value={selectedRows.find((item) => item.feeId === row.feeId)?.status ?? row.status}
              onChange={(e) => {
                const updatedRows = selectedRows.map((item) => {
                  if (item.feeId === row.feeId) {
                    return { ...item, status: Number(e.target.value) };
                  }
                  return item;
                });
                setEditedRows((prev) => {
                  const exists = prev.find((item) => item.feeId === row.feeId);
                  if (exists) {
                    // Update the existing item
                    return prev.map((item) =>
                      item.feeId === row.feeId ? { ...item, status: Number(e.target.value) } : item
                    );
                  } else {
                    // Add new edited item
                    return [...prev, { ...row, status: Number(e.target.value) }];
                  }
                });
              }}
            >
              <FormControlLabel control={<Radio size="small" value={1} />} label="Active" />
              <FormControlLabel control={<Radio size="small" value={0} />} label="Inactive" />
            </RadioGroup>
          );
        },
      },
      {
        name: 'actions',
        headerLabel: 'Actions',
        renderCell: (row) => {
          return (
            <Stack minWidth={80} direction="row" gap={1}>
              <IconButton size="small" onClick={() => handleEditBasicFeeList(row)} sx={{ padding: 0.5 }}>
                <ModeEditIcon />
              </IconButton>
              <IconButton size="small" color="error" sx={{ padding: 0.5 }} onClick={() => handleDeleteTermFeeList(row)}>
                <DeleteIcon />
              </IconButton>
            </Stack>
          );
        },
      },
    ],
    [handleEditBasicFeeList, selectedRows, handleDeleteTermFeeList]
  );
  const getRowKey = useCallback((row: any) => row.termId, []);

  return showCreateMultiple === 'multiple' ? (
    <CreateMultipleTitleForm onBackClick={() => setShowCreateMultiple('list')} />
  ) : (
    <Page title="List">
      <ListRoot>
        <Card className="Card" elevation={1} sx={{ px: { xs: 3, md: 5 }, py: { xs: 2, md: 3 } }}>
          <Box display="flex" pb={0.5} justifyContent="space-between">
            <Typography variant="h6" fontSize={17}>
              Basic Fee List
            </Typography>
            <Stack direction="row" alignItems="center">
              {selectedRows.length > 0 && (
                <Tooltip title="Delete All">
                  <IconButton
                    sx={{ visibility: { xs: 'hidden', sm: 'visible' }, mr: 1 }}
                    aria-label="delete"
                    color="error"
                    onClick={handleDeleteMultiple}
                  >
                    <DeleteIcon />
                  </IconButton>
                </Tooltip>
              )}
              <Stack direction="row" alignItems="center" gap={1}>
                <Button sx={{ borderRadius: '20px' }} variant="outlined" size="small" onClick={handleAddTermFeeList}>
                  <MdAdd size="20px" /> Create
                </Button>
                <Button
                  sx={{ borderRadius: '20px' }}
                  variant="outlined"
                  size="small"
                  onClick={() => setShowCreateMultiple('multiple')}
                >
                  <MdAdd size="20px" /> Create Multiple
                </Button>
              </Stack>

              {showFilter === false ? (
                <Tooltip title="Search">
                  <IconButton
                    aria-label="delete"
                    color="primary"
                    sx={{ ml: 1 }}
                    onClick={() => setShowFilter((x) => !x)}
                  >
                    <SearchIcon />
                  </IconButton>
                </Tooltip>
              ) : (
                <IconButton aria-label="delete" color="primary" sx={{ ml: 1 }} onClick={() => setShowFilter((x) => !x)}>
                  <IoIosArrowUp />
                </IconButton>
              )}
            </Stack>
          </Box>

          <Divider />
          <div className="card-main-body">
            <Collapse in={showFilter}>
              <form noValidate onReset={handleReset}>
                <Grid pb={2} pt={1} container spacing={3} alignItems="end">
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Academic Year
                      </Typography>
                      <Select
                        labelId="academicYearFilter"
                        id="academicYearFilterSelect"
                        value={academicYearFilter.toString()}
                        onChange={handleYearChange}
                        placeholder="Select Year"
                      >
                        <MenuItem value={0} sx={{ display: 'none' }}>
                          Select Year
                        </MenuItem>
                        {YearData.map((opt) => (
                          <MenuItem key={opt.accademicId} value={opt.accademicId}>
                            {opt.accademicTime}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Select Type
                      </Typography>
                      <Select
                        labelId="feeTypeFilter"
                        id="feeTypeFilterSelect"
                        value={feeTypeFilter.toString()}
                        onChange={handleFeeTypeChange}
                        placeholder="Select Type"
                      >
                        <MenuItem sx={{ display: 'none' }} value={0}>
                          Select Type
                        </MenuItem>
                        {FEE_TYPE_ID_OPTIONS.map((opt) => (
                          <MenuItem key={opt.id} value={opt.id}>
                            {opt.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xxl={2} xl={2.5} lg={3} md={4} sm={4} xs={12}>
                    <FormControl fullWidth>
                      <Typography variant="subtitle1" fontSize={12} color="GrayText">
                        Fee Title
                      </Typography>
                      <TextField
                        placeholder="Enter"
                        name="basicTitleFilter"
                        value={basicTitleFilter}
                        onChange={(e) => {
                          setBasicTitleFilter(e.target.value);
                          loadBasicFeeList({
                            ...currentBasicFeeListRequest,
                            feeTitle: e.target.value,
                          });
                        }}
                      />
                    </FormControl>
                  </Grid>
                  <Grid item lg="auto">
                    <Stack spacing={2} direction="row" sx={{ marginBottom: '2px' }}>
                      <Button type="reset" variant="contained" color="secondary" fullWidth>
                        Reset
                      </Button>
                    </Stack>
                  </Grid>
                </Grid>
              </form>
            </Collapse>
            {feeTypeFilter !== 0 && (
              <Box mt={!showFilter ? 2 : 0} display="flex" justifyContent="end">
                <LoadingButton
                  loading={isSubmitting}
                  onClick={handleUpdateAll}
                  disabled={selectedRows.length === 0}
                  color="warning"
                  startIcon={<SaveIcon fontSize="small" />}
                  size="small"
                  variant="contained"
                  sx={{ py: 0.2, px: 1, fontSize: 12 }}
                >
                  Update All
                  {/* {switchToUpdateButton ? 'Update All ' : 'Save All'} */}
                </LoadingButton>
              </Box>
            )}
            {feeTypeFilter !== 0 ? (
              <Paper className="card-table-container" sx={{ marginTop: '12px' }}>
                <DTVirtuoso
                  tableStyles={{ minWidth: { xs: '700px', lg: '1200px', xl: '100% ' } }}
                  showHorizontalScroll
                  ShowCheckBox
                  setSelectedRows={setSelectedRows}
                  selectedRows={selectedRows}
                  RowSelected
                  columns={basicFeeListColumns}
                  data={paginatedData}
                  getRowKey={getRowKey}
                  fetchStatus={basicFeeListStatus}
                  PaginationProps={pageProps}
                  allowPagination
                />
              </Paper>
            ) : (
              <Paper
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  border: 1,
                  borderColor: theme.palette.grey[300],
                  width: '100%',
                  height: { xs: 'calc(100vh - 400px)', sm: 'calc(100vh - 400px)', lg: 'calc(100vh - 300px)' },
                }}
              >
                <Stack direction="column" alignItems="center">
                  <img src={NoData} width="150px" alt="" />
                  <Typography variant="subtitle2" mt={2} color="GrayText">
                    No data found !
                  </Typography>
                </Stack>
              </Paper>
            )}
          </div>
        </Card>
      </ListRoot>
      <TemporaryDrawer
        onClose={toggleDrawerClose}
        state={drawerOpen}
        Title={selectedBasicFeeDetail === DefaultBasicFeeInfo ? 'Create Fee Title' : 'Edit Fee Title'}
        DrawerContent={
          <CreateEditBasicFeeTitleForm
            basicFeeDetails={selectedBasicFeeDetail}
            onSave={handleSaveOrEdit}
            onCancel={toggleDrawerClose}
          />
        }
      />
    </Page>
  );
}

export default BasicFeeList;
