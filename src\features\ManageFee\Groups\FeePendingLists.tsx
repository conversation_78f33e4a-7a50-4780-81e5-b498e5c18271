/* eslint-disable no-nested-ternary */
import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import Page from '@/components/shared/Page';
import { Box, ToggleButtonGroup, ToggleButton, Typography, useTheme } from '@mui/material';
import styled from 'styled-components';
import BasicFeePendingList from '@/features/ManageFee/PendingList/BasicFeePendingList';
import TermFeePendingList from '@/features/ManageFee/PendingList/TermFeePendingList';

const FeePendingListsRoot = styled.div`
  padding: 0.5rem 1rem 1rem 1rem;
  @media screen and (max-width: 576px) {
    padding: 0.5rem;
  }

  .toggle-container {
    padding-bottom: 0.5rem;
    padding-left: 0.5rem;
    background-color: ${(props) =>
      props.theme.themeMode === 'light' ? props.theme.palette.grey[100] : props.theme.palette.grey[900]};
    border-radius: 8px;
  }

  .content-container {
    /* Remove Page wrapper padding since components have their own */
    margin: -1rem;
    @media screen and (max-width: 576px) {
      margin: -0.5rem;
    }
  }
`;

const options = [
  { path: '/manage-fee/total-paid-list', Component: <TotalFeePendingList /> },
  { path: '/manage-fee/basic-fee', Component: <BasicFeePendingList /> },
  { path: '/manage-fee/term-fee', Component: <TermFeePendingList /> },
];

type FeeType = 'total' | 'basic' | 'term';

function FeePendingLists() {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const [feeType, setFeeType] = useState<FeeType>('basic');

  // Sync URL with fee type selection
  useEffect(() => {
    const currentPath = location.pathname;
    if (currentPath.includes('term-fee')) {
      setFeeType('term');
    } else {
      setFeeType('basic');
    }
  }, [location.pathname]);

  const handleFeeTypeChange = (event: React.MouseEvent<HTMLElement>, newFeeType: FeeType | null) => {
    if (newFeeType !== null) {
      setFeeType(newFeeType);
      if (newFeeType === 'basic') {
        navigate('/manage-fee/fee-pending-lists/basic-fee', { replace: true });
      } else {
        navigate('/manage-fee/fee-pending-lists/term-fee', { replace: true });
      }
    }
  };

  return (
    <Page title="Fee Pending Lists">
      <FeePendingListsRoot>
        <div className="toggle-container">
          <Box display="flex" flexDirection="row" alignItems="center" justifyContent="center" gap={2}>
            <Typography
              variant="h6"
              fontSize={18}
              fontWeight={600}
              color={theme.palette.mode === 'light' ? 'inherit' : 'text.primary'}
            >
              Fee Pending Lists
            </Typography>
            <ToggleButtonGroup
              value={feeType}
              exclusive
              onChange={handleFeeTypeChange}
              aria-label="Fee Type Selection"
              size="small"
              sx={{
                '& .MuiToggleButton-root': {
                  px: 3,
                  py: 1,
                  fontWeight: 600,
                  borderRadius: '20px',
                  border: `2px solid ${theme.palette.primary.main}`,
                  color: theme.palette.primary.main,
                  '&.Mui-selected': {
                    backgroundColor: theme.palette.primary.main,
                    color: theme.palette.primary.contrastText,
                    '&:hover': {
                      backgroundColor: theme.palette.primary.dark,
                    },
                  },
                  '&:hover': {
                    backgroundColor: theme.palette.primary.light,
                    color: theme.palette.primary.contrastText,
                  },
                },
              }}
            >
              <ToggleButton value="total">Total</ToggleButton>
              <ToggleButton value="basic">Fee Wise</ToggleButton>
              <ToggleButton value="term">Term Wise</ToggleButton>
            </ToggleButtonGroup>
          </Box>
        </div>

        <div className="content-container">{options[feeType].Component}</div>
      </FeePendingListsRoot>
    </Page>
  );
}

export default FeePendingLists;
