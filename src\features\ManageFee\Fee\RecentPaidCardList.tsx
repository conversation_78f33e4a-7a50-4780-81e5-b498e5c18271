/* eslint-disable no-nested-ternary */
import React, { useCallback, useEffect, useMemo } from 'react';
import { Paper, Stack, Typography, Avatar, Chip, Box, Grid, Snackbar, Divider, IconButton } from '@mui/material';
import PrintIcon from '@mui/icons-material/Print';
import PictureAsPdfRoundedIcon from '@mui/icons-material/PictureAsPdfRounded';
import Button from '@mui/material/Button';
import dayjs from 'dayjs';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import MuiAlert from '@mui/material/Alert';
import NoData from '@/assets/no-datas.png';
import { GetFeeOverviewPaidListType } from '@/types/ManageFee';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import UndoIcon from '@mui/icons-material/Undo';
import { SuccessIcon } from '@/theme/overrides/CustomIcons';
import { useAppDispatch } from '@/hooks/useAppDispatch';
import { StudentListRequest } from '@/types/StudentManagement';
import { fetchStudentList } from '@/store/Students/studentManagement.thunks';
import { getStudentListData, getStudentListStatus } from '@/config/storeSelectors';
import { useAppSelector } from '@/hooks/useAppSelector';
import useAuth from '@/hooks/useAuth';
import man from '@/assets/man.png';
import woman from '@/assets/woman.png';

export function RecentPaidCardList({ data, onPrint, onUnpay, onApprove, onPdf, theme, classId }: any) {
  const { user } = useAuth();
  const adminId: number = user ? user.accountId : 0;
  const dispatch = useAppDispatch();
  const StudentListStatus = useAppSelector(getStudentListStatus);
  const StudentListData = useAppSelector(getStudentListData);
  const [snackbarOpen, setSnackbarOpen] = React.useState(false);
  const [snackbarMsg, setSnackbarMsg] = React.useState('');

  const currentStudentListRequest = useMemo(
    () => ({
      pageNumber: 1,
      pageSize: 100,
      sortColumn: 'studentName',
      sortDirection: 'ASC',
      filters: {
        adminId,
        academicId: 11,
        classId,
        studentName: '',
        studentGender: 0,
        admissionDate: '',
        admissionNumber: '',
        studentDob: '',
        studentFatherName: '',
        studentFatherNumber: '',
        studentBloodGroup: '',
        studentCaste: '',
      },
    }),
    [adminId, classId]
  );

  const loadStudentList = useCallback(
    (request: StudentListRequest) => {
      dispatch(fetchStudentList(request));
    },
    [dispatch]
  );

  useEffect(() => {
    if (StudentListStatus === 'idle') {
      loadStudentList(currentStudentListRequest);
    }
  }, [loadStudentList, StudentListStatus, currentStudentListRequest]);

  const handleCopy = (value: string, label: string) => {
    navigator.clipboard.writeText(value);
    setSnackbarMsg(`${label} copied!`);
    setSnackbarOpen(true);
  };
  return (
    <>
      {data.length === 0 ? (
        <Box
          display="flex"
          alignItems="center"
          justifyContent="center"
          width="100%"
          height={{ xs: '100%', sm: '100%', lg: 465 }}
        >
          <Stack direction="column" alignItems="center">
            <img src={NoData} width="150px" alt="" />
            <Typography variant="subtitle2" mt={2} color="GrayText">
              No data found!
            </Typography>
          </Stack>
        </Box>
      ) : (
        <Grid container spacing={2} mb={3}>
          {data.map((row: GetFeeOverviewPaidListType) => {
            const matchedStudents =
              StudentListData?.filter((student: any) => student.studentId === row.studentId) || [];
            // Take the first matched student's gender (if any)
            // const studentGender = matchedStudents[0]?.studentGender;
            return (
              <Grid item xs={12} sm={12} md={6} lg={4} xl={4} xxl={4} key={row.receiptId}>
                <Paper
                  sx={{
                    // p: 2,
                    mt: 2,
                    // border: `1px solid ${theme.palette.grey[300]}`,
                    borderRadius: 2,
                    boxShadow: 2,
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 1,
                    height: '100%',
                    // overflow: 'hidden',
                    position: 'relative',
                    // borderTopLeftRadius: 30,
                    border: 1,
                    borderColor:
                      row.status === 'Payment Initiated'
                        ? theme.palette.warning.lighter
                        : row.status === 'Paid'
                        ? theme.palette.success.lighter
                        : row.status === 'Failed'
                        ? theme.palette.error.lighter
                        : row.status === 'Cancelled'
                        ? theme.palette.error.lighter
                        : row.status === 'Pending'
                        ? theme.palette.warning.lighter
                        : theme.palette.secondary.lighter,
                    // bgcolor:
                    //   row.status === 'Payment Initiated'
                    //     ? theme.palette.warning.lighter
                    //     : row.status === 'Paid'
                    //     ? theme.palette.success.lighter
                    //     : row.status === 'Failed'
                    //     ? theme.palette.error.lighter
                    //     : row.status === 'Cancelled'
                    //     ? theme.palette.error.lighter
                    //     : row.status === 'Pending'
                    //     ? theme.palette.warning.lighter
                    //     : theme.palette.secondary.lighter,
                  }}
                >
                  <Stack px={2} pt={2}>
                    <Stack direction="row" alignItems="start" justifyContent="space-between" gap={2} pl={0}>
                      <Stack direction="row" alignItems="center" gap={2} pl={0}>
                        <Stack
                          sx={{
                            // borderBottom: 2,
                            // borderRight: 2,
                            border: 2,
                            borderColor:
                              row.status === 'Payment Initiated'
                                ? theme.palette.warning.lighter
                                : row.status === 'Paid'
                                ? theme.palette.success.lighter
                                : row.status === 'Failed'
                                ? theme.palette.error.lighter
                                : row.status === 'Cancelled'
                                ? theme.palette.error.lighter
                                : row.status === 'Pending'
                                ? theme.palette.warning.lighter
                                : theme.palette.secondary.lighter,
                            // position: 'absolute',
                            // top: -4,
                            // left: -4,
                            borderRadius: 50,
                          }}
                        >
                          {matchedStudents.length > 0 ? (
                            matchedStudents.map((student: any, idx: number) => (
                              <Avatar
                                key={idx}
                                alt=""
                                src={
                                  student.studentGender === 0 ? man : student.studentGender === 1 ? woman : undefined
                                }
                                sx={{
                                  border: 1,
                                  borderColor: 'white',
                                  width: 40,
                                  height: 40,
                                  ml: idx === 0 ? 0 : -1, // overlap avatars
                                  zIndex: matchedStudents.length - idx,
                                  bgcolor: 'grey.100',
                                }}
                              />
                            ))
                          ) : (
                            <Avatar sx={{ width: 40, height: 40 }} />
                          )}
                        </Stack>
                        <Box>
                          <Typography variant="subtitle2" fontSize={12}>
                            {row.studentName}
                          </Typography>
                          <Stack direction="row" alignItems="center" gap={1}>
                            <Typography variant="body2" fontSize={12} color="text.secondary">
                              Class&nbsp;:
                            </Typography>
                            <Typography variant="subtitle2" fontSize={12}>
                              {row.className}
                            </Typography>
                          </Stack>
                        </Box>
                      </Stack>
                      <Stack direction="row" alignItems="center" gap={2} pl={0}>
                        <Chip
                          size="small"
                          label={row.status}
                          variant="filled"
                          color={
                            row.status === 'Payment Initiated'
                              ? 'warning'
                              : row.status === 'Paid'
                              ? 'success'
                              : row.status === 'Failed'
                              ? 'error'
                              : row.status === 'Cancelled'
                              ? 'error'
                              : row.status === 'Pending'
                              ? 'warning'
                              : 'secondary'
                          }
                          sx={{
                            ml: 'auto',
                            fontWeight: 600,
                            fontSize: 12,
                          }}
                        />
                      </Stack>
                    </Stack>
                    <Stack direction="row" gap={2} alignItems="center" pt={1} flexWrap="wrap">
                      <Stack direction="row" alignItems="center" gap={1}>
                        <Typography width={60} variant="body2" fontSize={12} color="text.secondary">
                          Amount
                        </Typography>
                        <Typography variant="body2" fontSize={12} color="text.secondary">
                          :
                        </Typography>
                        <Typography variant="subtitle2" fontSize={12} color={theme.palette.success.main}>
                          {Number(row.grandTotal).toLocaleString('en-IN')}
                        </Typography>
                      </Stack>
                      <Stack direction="row" alignItems="center" gap={1}>
                        <Typography variant="body2" fontSize={12} color="text.secondary">
                          Paid Date&nbsp;:
                        </Typography>
                        <Typography variant="subtitle2" fontSize={12}>
                          {dayjs(row.paidDate, 'YYYY-MM-DDTHH:mm:ss').format('DD/MM/YYYY, h:mm a')}
                        </Typography>
                      </Stack>
                    </Stack>
                    <Stack direction="row" alignItems="center" gap={1}>
                      <Typography width={60} variant="caption" fontSize={12} color="text.secondary">
                        Order No
                      </Typography>
                      <Typography variant="body2" fontSize={12} color="text.secondary">
                        :
                      </Typography>
                      <Typography variant="subtitle2" fontSize={12}>
                        {row.billdeskOrderNumber}
                      </Typography>
                      {row.billdeskOrderNumber && (
                        <IconButton
                          size="small"
                          onClick={() => handleCopy(row.billdeskOrderNumber, 'Order Number')}
                          sx={{ p: 0.5 }}
                        >
                          <ContentCopyIcon sx={{ fontSize: 12 }} />
                        </IconButton>
                      )}
                    </Stack>
                    <Stack direction="row" alignItems="center" gap={1}>
                      <Typography width={60} variant="caption" fontSize={12} color="text.secondary">
                        Txn Id
                      </Typography>
                      <Typography variant="body2" fontSize={12} color="text.secondary">
                        :
                      </Typography>
                      <Typography variant="subtitle2" fontSize={12}>
                        {row.billdeskTransactionId}
                      </Typography>
                      {row.billdeskTransactionId && (
                        <IconButton
                          size="small"
                          onClick={() => handleCopy(row.billdeskTransactionId, 'Transaction Id')}
                          sx={{ p: 0.5 }}
                        >
                          <ContentCopyIcon sx={{ fontSize: 12 }} />
                        </IconButton>
                      )}
                    </Stack>
                  </Stack>
                  <Divider sx={{ borderColor: theme.palette.grey[400], borderBottomWidth: 1 }} />
                  <Stack direction="row" gap={1} mt={1} px={2}>
                    <Button
                      fullWidth
                      disabled={row.status === 'Cancelled'}
                      size="small"
                      color="success"
                      startIcon={<PrintIcon />}
                      variant="outlined"
                      onClick={() => onPrint(row)}
                      sx={{ fontSize: 12 }}
                    >
                      Print
                    </Button>
                    <Button
                      fullWidth
                      disabled={row.status === 'Cancelled'}
                      size="small"
                      color="error"
                      startIcon={<UndoIcon />}
                      variant="outlined"
                      onClick={() => onUnpay(row)}
                      sx={{ fontSize: 12 }}
                    >
                      Unpay
                    </Button>
                    <Button
                      fullWidth
                      disabled={row.status === 'Paid'}
                      size="small"
                      color="primary"
                      startIcon={<SuccessIcon />}
                      variant="outlined"
                      onClick={() => onApprove(row)}
                      sx={{ fontSize: 12 }}
                    >
                      Approve
                    </Button>
                    <Button
                      fullWidth
                       disabled={row.status === 'Cancelled'}
                      size="small"
                      color="info"
                      startIcon={<PictureAsPdfRoundedIcon />}
                      variant="outlined"
                      onClick={() => onPdf(row)}
                      sx={{ fontSize: 12 }}
                    >
                      PDF
                    </Button>
                  </Stack>
                </Paper>
              </Grid>
            );
          })}
        </Grid>
      )}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={2000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <MuiAlert severity="success" sx={{ width: '100%' }}>
          {snackbarMsg}
        </MuiAlert>
      </Snackbar>
    </>
  );
}
